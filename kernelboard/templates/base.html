<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}GPU MODE{% endblock %}</title>

    <!-- Em<PERSON><PERSON> -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>⚡</text></svg>">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">

    {% block extra_head %}{% endblock %}
</head>
<body class="bg-white">
    <!-- Header -->
    <header class="header-base">
        <div class="header-container">
            <div class="header-layout">
                <div class="header-row">
                    <a href="{{ url_for('index') }}" class="header-brand-item">
                        <span class="header-brand-icon">⚡</span>
                        <span>GPU MODE</span>
                    </a>
                    <div class="header-item">
                        <a href="{{ url_for('news') }}" class="header-link">
                            News
                        </a>
                    </div>
                    <div class="header-item">
                        <a href="https://www.youtube.com/@GPUMODE" target="_blank" class="header-link external-link">
                            Lectures
                        </a>
                    </div>
                    <div class="header-item">
                        <a href="https://github.com/gpu-mode/resource-stream" target="_blank" class="header-link external-link">
                            Resources
                        </a>
                    </div>
                    <div class="header-item">
                        <a href="https://gpu-mode.github.io/discord-cluster-manager/docs/intro" target="_blank" class="header-link external-link">Docs</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Error Toast Messages Container (Top Center) -->
    <div id="toast-container-error" class="toast-container-error">
        {# Error toasts will be dynamically added here by JavaScript #}
    </div>

    <!-- Default Toast Messages Container (Bottom Center) -->
    <div id="toast-container-default" class="toast-container-default">
        {# Default toasts will be dynamically added here by JavaScript #}
    </div>

    <!-- Hidden Template for Error Flash Messages -->
    <div hidden>
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    {# Process only 'error' category for toasts here #}
                    {% if category == 'error' %}
                        <div class="toast-template-error"
                             data-message="{{ message }}">
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Hidden Template for Default Flash Messages -->
    <div hidden>
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    {# Process only default 'message' category for toasts here #}
                    {% if category == 'message' or category is none %}
                        <div class="toast-template-default"
                             data-message="{{ message }}">
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="main-container">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer-base">
        <div class="footer-container">
            <a href="https://discord.gg/gpumode" class="footer-link">Discord</a>
            <a href="https://x.com/GPU_MODE" class="footer-link">X</a>
            <a href="https://www.youtube.com/@GPUMODE" class="footer-link">YouTube</a>
            <a href="https://github.com/gpu-mode/" class="footer-link">GitHub</a>
            <span class="footer-text">© 2025 GPU MODE</span>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/flash.js') }}"></script> {# Ensure flash script is linked #}
    {% block scripts %}{% endblock %}
</body>
</html>
