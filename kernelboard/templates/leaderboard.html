{% extends "base.html" %}

{% block title %}{{ name }} - Kernel Leaderboards{% endblock %}

{# Determine if we need to load MathJax. #}
{% set needs_mathjax = '\\(' in description or '$$' in description or '\\begin' in description %}

{% block extra_head %}
    {{ super() }}
    {% if needs_mathjax %}
    <script id="mathjax" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    {% endif %}
{% endblock %}

{% block content %}
<script src="{{ url_for('static', filename='js/leaderboard.js') }}"></script>

<div class="content-stack">
    <h1 class="font-mono">
        {% set color = name|to_color %}
        <div class="{{ color }}-square"></div>
        {{ name }}
    </h1>
    
    <div class="leaderboard-grid">
        <div class="leaderboard-card">
            <h2>Deadline</h2>
            <p>
                {% if time_left != None %}
                    <span class="font-medium">{{ time_left }}</span> ({{ deadline|format_datetime }})
                {% else %}
                    {{ deadline }}
                {% endif %}
            </p>
        </div>

        <div class="leaderboard-card">
            <h2>Language</h2>
            <p>{{ lang }}</p>
        </div>

        <div class="leaderboard-card">
            <h2>GPU Type{{ 's' if gpu_types|length > 1 or gpu_types|length == 0}}</h2>
            <p>{{ gpu_types|join(', ') if gpu_types|length > 0 else 'None' }}</p>
        </div>
    </div>
    
    <div>
        <div class="leaderboard-card">
            <h2>Description</h2>
            <p class="whitespace-pre-wrap leading-relaxed">{{ description }}</p>
        </div>
    </div>

    {# The 'max-h-[300px] overflow-y-hidden' is needed for toggleCodeBtn. #}
    <div id="referenceImpl" class="leaderboard-card relative max-h-[300px] overflow-y-hidden">
        <div class="absolute top-2 right-2 flex space-x-2">
            <button id="toggleCodeBtn" class="code-block-btn">Show</button>
            <button id="copyCodeBtn" class="code-block-btn">Copy</button>
        </div>
        <h2>Reference Implementation</h2>
        <pre><code id="codeBlock">{{ reference }}</code></pre>
    </div>

    {# If there are rankings, show rankings tables. #}
    {% set has_rankings = rankings is defined and rankings|map('length')|list|sum > 0 %}

    {% if has_rankings %}
        <h1>Rankings</h1>

        <div class="ranking-container">
            {% for gpu_type, ranking in rankings.items() %}
                {% if ranking|length > 0 %}
                    <div class="ranking-section" id="section-{{ gpu_type|lower|replace(' ', '-') }}">
                        <div class="flex justify-between items-center mb-2">
                            <h2 class="font-mono">
                                <span class="{{ color }}-chip px-3 py-1">
                                    {{ gpu_type }}
                                </span>
                            </h2>

                            {% if ranking|length > 3 %}
                                <button class="rankings-btn" 
                                        data-gpu-id="{{ gpu_type|lower|replace(' ', '-') }}"
                                        data-count="{{ ranking|length }}">
                                    Show All ({{ ranking|length }})
                                </button>
                            {% endif %}
                        </div>

                        <table class="ranking-table">
                            <tbody class="ranking-table-row">
                                {% for entry in ranking %}
                                    {% set user = entry['user_name'] %}
                                    {% set score = entry['score'] %}
                                    {% set file_name = entry['file_name'] %}
                                    {% set rank = entry['rank'] %}
                                    {% set prev_score = entry['prev_score'] %}
                                    <tr class="rank-row {% if rank > 3 %}hidden-row{% endif %}" data-rank="{{ rank }}">
                                        <td>
                                            {% if rank == 1 %}
                                                <span class="font-bold">{{ user }} <span class="medal gold">🥇</span></span>
                                            {% elif rank == 2 %}
                                                <span class="font-bold">{{ user }} <span class="medal silver">🥈</span></span>
                                            {% elif rank == 3 %}
                                                <span class="font-bold">{{ user }} <span class="medal bronze">🥉</span></span>
                                            {% else %}
                                                {{ user }}
                                            {% endif %}
                                        </td>
                                        <td class="font-mono">
                                            {{ score|format_score }}
                                            {% if prev_score != None %}
                                                &nbsp;
                                                <span class="text-gray-500">+{{ prev_score|format_score }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="font-mono">{{ file_name }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% endif %}
            {% endfor %}
        </div>
    {% else %}
        <h2>No submissions yet</h2>
        <p>Be the first to submit a solution for this challenge!</p>
    {% endif %}
</div>

{% endblock %} 