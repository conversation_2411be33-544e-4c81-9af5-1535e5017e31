/*
Generate main.css by running the following command from src/kernelboard/:

  npx tailwindcss -i ./kernelboard/static/css/input.css -o \
    ./kernelboard/static/css/main.css --verbose

Appending --watch will watch for changes and update main.css automatically.
*/

@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes shine {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes error-toast-in {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes error-toast-out {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

@keyframes toast-in {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes toast-out {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(100%);
  }
}

.animate-error-toast-in {
  animation: error-toast-in 0.3s ease-out forwards;
}
.animate-error-toast-out {
  animation: error-toast-out 0.3s ease-in forwards;
}

.animate-default-toast-in {
  animation: toast-in 0.3s ease-out forwards;
}

.animate-default-toast-out {
  animation: toast-out 0.3s ease-in forwards;
}

@layer base {
  html {
    @apply overflow-y-scroll;
  }
  body {
    @apply flex flex-col min-h-screen;
  }
  h1 {
    @apply text-3xl mt-4 font-bold text-dark;
  }
  h2 {
    @apply text-2xl mt-4 font-bold text-dark;
  }
  h3 {
    @apply text-xl mt-4 font-bold text-dark;
  }
}

@layer components {
  .colored-square {
    @apply w-4 h-4 rounded inline-block mr-2;
  }
  .coral-square {
    @apply colored-square;
    background-color: #FF6B6B;
  }
  .turquoise-square {
    @apply colored-square;
    background-color: #4ECDC4;
  }
  .sky-blue-square {
    @apply colored-square;
    background-color: #45B7D1;
  }
  .gray-aquamarine-square {
    @apply colored-square;
    background-color: #96CEB4;
  }
  .pale-yellow-square {
    @apply colored-square;
    background-color: #FFEEAD;
  }
  .dusky-rose-square {
    @apply colored-square;
    background-color: #D4A5A5;
  }
  .purple-square {
    @apply colored-square;
    background-color: #9B5DE5;
  }
  .medium-pink-square {
    @apply colored-square;
    background-color: #F15BB5;
  }
  .bright-blue-square {
    @apply colored-square;
    background-color: #00BBF9;
  }
  .aquamarine-square {
    @apply colored-square;
    background-color: #00F5D4;
  }
  .medal {
    @apply inline-block ml-[5px];
    animation: shine 2s infinite;
  }
  .medal .gold {
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
  }
  .medal .silver {
    text-shadow: 0 0 5px rgba(192, 192, 192, 0.5);
  }
  .medal .bronze {
    text-shadow: 0 0 5px rgba(205, 127, 50, 0.5);
  }
  .external-link::after {
    content: '\2197\FE0E'; /* Unicode for up-right arrow, ↗ (2197) displayed as text (FE0E) instead of emoji */
    @apply inline-block ml-0.5 text-xs align-middle no-underline;
  }
  .content-stack {
    @apply space-y-6;
  }
  .page-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full;
  }
  .header-base {
    @apply bg-white border-b;
  }
  .header-container {
    @apply page-container;
  }
  .header-layout {
    @apply flex items-center justify-between h-16;
  }
  .header-row {
    @apply flex items-center;
  }
  .header-brand-item {
    @apply font-bold text-xl text-gray-900 flex items-center;
  }
  .header-brand-icon {
    @apply text-lg mr-1;
  }
  .header-item {
    @apply ml-10 flex items-baseline space-x-4;
  }
  .header-link {
    @apply px-3 py-2 rounded-md text-sm font-medium text-gray-900 hover:bg-gray-100;
  }
  .header-login-btn {
    @apply px-4 py-2 rounded-full bg-discord text-sm font-medium text-white hover:bg-discord/80;
  }
  .main-container {
    @apply page-container flex-grow py-8;
  }
  .footer-base {
    @apply bg-white border-t;
  }
  .footer-container {
    @apply page-container text-center text-sm py-4 text-gray-600 space-y-6 space-x-6;
  }
  .footer-link {
    @apply text-gray-600 hover:text-gray-900;
  }
  .footer-text {
    @apply text-gray-600;
  }
  .error-layout {
    @apply grid grid-cols-2 gap-8 items-center;
  }
  .error-message {
    @apply text-left;
  }
  .error-image {
    @apply flex justify-center;
  }
  .error-headline {
    @apply text-4xl font-bold mb-4;
  }
  .error-description {
    @apply text-gray-600 mb-8 font-mono
  }
  .leaderboard-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }
  .leaderboard-tile-link {
    @apply block hover:no-underline h-full;
  }
  .leaderboard-tile {
    @apply p-4 bg-white rounded-lg h-full border border-neutral hover:border-secondary transition-colors;
  }
  .leaderboard-tile-name {
    @apply font-mono text-lg mb-2 text-dark;
  }
  .leaderboard-content {
    @apply text-dark/70;
  }
  .score-box {
    @apply bg-white/10 rounded-lg p-3 space-y-1 border border-neutral/50;
  }
  .score-row {
    @apply flex justify-between;
  }
  .score {
    @apply font-mono;
  }
  .chip {
    @apply inline-block rounded-full;
  }
  .coral-chip {
    @apply chip;
    background-color: #FF6B6B40;
  }
  .turquoise-chip {
    @apply chip;
    background-color: #4ECDC440;
  }
  .sky-blue-chip {
    @apply chip;
    background-color: #45B7D140;
  }
  .gray-aquamarine-chip {
    @apply chip;
    background-color: #96CEB440;
  }
  .pale-yellow-chip {
    @apply chip;
    background-color: #FFEEAD40;
  }
  .dusky-rose-chip {
    @apply chip;
    background-color: #D4A5A540;
  }
  .purple-chip {
    @apply chip;
    background-color: #9B5DE540;
  }
  .medium-pink-chip {
    @apply chip;
    background-color: #F15BB540;
  }
  .bright-blue-chip {
    @apply chip;
    background-color: #00BBF940;
  }
  .aquamarine-chip {
    @apply chip;
    background-color: #00F5D440;
  }
  .leaderboard-grid {
    @apply grid grid-cols-1 md:grid-cols-3 gap-6 mb-8;
  }
  .leaderboard-card {
    @apply p-4 bg-gray-50 rounded-lg shadow-sm border border-gray-200;
  }
  .leaderboard-card h2 {
    @apply font-bold mt-0 mb-2;
  }
  .leaderboard-grid .leaderboard-card p {
    @apply text-gray-800;
  }
  .code-block-btn {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm;
  }
  .code-block-fade {
    @apply absolute bottom-0 left-0 w-full pointer-events-none z-10;
    height: 75%;
    background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 120%);
  }
  .ranking-table {
    @apply min-w-full divide-y divide-gray-200;
  }
  .ranking-table-row {
    @apply bg-white divide-y divide-gray-200 px-6 py-4 whitespace-nowrap;
  }
  .ranking-container .ranking-table {
    @apply w-full table-fixed;
  }
  .ranking-container .ranking-table th:nth-child(1),
  .ranking-container .ranking-table td:nth-child(1) {
    @apply w-1/3;
  }
  .ranking-container .ranking-table th:nth-child(2),
  .ranking-container .ranking-table td:nth-child(2) {
    @apply w-1/3;
  }
  .ranking-container .ranking-table th:nth-child(3),
  .ranking-container .ranking-table td:nth-child(3) {
    @apply w-1/3;
  }
  .ranking-container .ranking-table td {
    @apply whitespace-nowrap overflow-hidden text-ellipsis;
  }
  .ranking-container .ranking-table .hidden-row {
    @apply hidden;
  }
  .ranking-section {
    @apply mb-8;
  }
  .rankings-btn {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-600 px-3 py-1 rounded text-sm;
  }
  .news-item a {
    @apply text-secondary underline hover:brightness-75
  }
  .news-item h2 {
    @apply mb-4
  }
  .news-item p {
    @apply mb-4
  }
  .login-box {
    @apply flex flex-col justify-center items-center min-h-[60vh] text-center;
  }
  .login-button {
    @apply bg-discord text-white px-8 py-4 rounded-md text-base cursor-pointer no-underline inline-block mb-5 hover:bg-discord-darker transition-all duration-200 ease-in-out;
  }
  .toast-container-error {
    @apply fixed top-20 left-1/2 -translate-x-1/2 z-50;
    @apply flex flex-col items-center space-y-2;
    @apply w-full max-w-sm pointer-events-none bg-white;
  }
  .toast-error {
    @apply pointer-events-auto w-full overflow-hidden rounded-lg shadow-lg;
    @apply ring-1 ring-toast-error/20;
    @apply text-toast-error bg-toast-error/10;
    @apply p-4;
  }
  .toast-error-content {
    @apply flex items-start;
  }
  .toast-error-message {
    @apply text-sm font-medium;
  }
  .toast-error-close-btn {
    @apply ml-auto;
    @apply pl-3;
    @apply inline-flex rounded-md bg-transparent text-current opacity-70 hover:opacity-100;
    @apply focus:outline-none focus:ring-2 focus:ring-toast-error focus:ring-offset-2;
  }
  .toast-container-error {
    @apply fixed top-20 left-1/2 -translate-x-1/2 z-50;
    @apply flex flex-col items-center space-y-2;
    @apply w-full max-w-sm pointer-events-none bg-white;
  }
  .toast-error {
    @apply pointer-events-auto w-full overflow-hidden rounded-lg shadow-lg;
    @apply ring-1 ring-toast-error/20;
    @apply text-toast-error bg-toast-error/10;
    @apply p-4;
  }
  .toast-error-content {
    @apply flex items-start;
  }
  .toast-error-message {
    @apply text-sm font-medium;
  }
  .toast-error-close-btn {
    @apply ml-auto;
    @apply pl-3;
    @apply inline-flex rounded-md bg-transparent text-current opacity-70 hover:opacity-100;
    @apply focus:outline-none focus:ring-2 focus:ring-toast-error focus:ring-offset-2;
  }
  .toast-container-default {
    @apply fixed bottom-4 left-1/2 -translate-x-1/2 z-50;
    @apply flex flex-col items-center space-y-2;
    @apply w-full max-w-sm pointer-events-none bg-white;
  }
  .toast-default {
    @apply pointer-events-auto w-full overflow-hidden rounded-lg shadow-lg;
    @apply ring-1 ring-toast-default/20;
    @apply text-toast-default bg-toast-default/5;
    @apply p-4;
  }
  .toast-default-content {
    @apply flex items-start;
  }
  .toast-default-message {
    @apply text-sm font-medium;
  }
  .toast-default-close-btn {
    @apply ml-auto;
    @apply pl-3;
    @apply inline-flex rounded-md bg-transparent text-current opacity-70 hover:opacity-100;
    @apply focus:outline-none focus:ring-2 focus:ring-toast-default focus:ring-offset-2;
  }
}
