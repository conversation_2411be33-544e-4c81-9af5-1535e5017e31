.homepage-header {
  height: 800px;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  font-weight: 300;
  width: 100%;
}

.header-title {
  font-size: 3rem;
}

@media (min-width: 768px) {
  .header-title {
    font-size: 4rem;
  }
}

.header-subtitle {
  font-size: 1.5rem;
}

.header-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  max-width: 680px;
}

.header-links a {
  display: inline-block;
  transition: transform 0.2s ease-in-out;
}

.header-links a:hover {
  transform: translateY(-2px);
}
