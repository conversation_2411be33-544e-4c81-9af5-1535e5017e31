/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

.heroBanner {
  padding: 4rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

@media screen and (max-width: 996px) {
  .heroBanner {
    padding: 2rem;
  }
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
}

.leaderboardSection {
  padding: 2rem 0;
}

.timestamp {
  text-align: center;
  color: var(--ifm-color-secondary-darker);
  font-size: 0.875rem;
  margin-bottom: 2rem;
}

.leaderboardGroup {
  background: var(--ifm-background-surface-color);
  border: 1px solid var(--ifm-color-emphasis-300);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.leaderboardGroup:hover {
  border-color: var(--ifm-color-primary);
}

.leaderboardGroup.selected {
  border-color: var(--ifm-color-primary);
  box-shadow: 0 0 0 1px var(--ifm-color-primary);
}

.leaderboardName {
  color: var(--ifm-color-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.leaderboardDetails {
  margin-left: 1rem;
}

.deadline {
  color: var(--ifm-color-secondary-darker);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.gpuTypesContainer {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.gpuTypeGroup {
  background: var(--ifm-background-surface-color);
  border: 1px solid var(--ifm-color-emphasis-300);
  border-radius: 8px;
  padding: 1.5rem;
  position: relative;
}

.gpuTypeGroup:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 10%;
  right: 10%;
  height: 2px;
  background: linear-gradient(to right, transparent, var(--ifm-color-emphasis-300), transparent);
}

.gpuTypeTitle {
  color: var(--ifm-color-primary);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--ifm-color-emphasis-200);
}

.problemsContainer {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.problemGroup {
  background: linear-gradient(145deg, var(--ifm-color-emphasis-100), var(--ifm-background-surface-color));
  border-radius: 16px;
  padding: 2rem;
  margin: 1.5rem 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.problemName {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  background: linear-gradient(90deg, var(--ifm-color-primary), var(--ifm-color-primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.problemDeadline {
  font-size: 0.9rem;
  color: var(--ifm-color-emphasis-600);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.submissionsList {
  background: var(--ifm-background-surface-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.submission {
  padding: 1rem 1.5rem;
  font-size: 1rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--ifm-color-emphasis-200);
  color: var(--ifm-font-color-base);
}

.submission:last-child {
  border-bottom: none;
}

.submission:hover {
  background: var(--ifm-color-emphasis-100);
  transform: translateX(4px);
}

.first {
  background: linear-gradient(90deg, rgba(255, 215, 0, 0.15), transparent);
  font-weight: 700;
  color: #FFB800;
}

.second {
  background: linear-gradient(90deg, rgba(192, 192, 192, 0.15), transparent);
  font-weight: 700;
  color: #A7A7A7;
}

.third {
  background: linear-gradient(90deg, rgba(205, 127, 50, 0.15), transparent);
  font-weight: 700;
  color: #CD7F32;
}

.first:hover {
  background: linear-gradient(90deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
}

.second:hover {
  background: linear-gradient(90deg, rgba(192, 192, 192, 0.2), rgba(192, 192, 192, 0.1));
}

.third:hover {
  background: linear-gradient(90deg, rgba(205, 127, 50, 0.2), rgba(205, 127, 50, 0.1));
}

.paginationControls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2rem;
}

.pageButton {
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  background: linear-gradient(135deg, #ff8c42, #ff6b00);
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.pageButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.pageButton:disabled {
  background: linear-gradient(135deg, #ffc5a3, #ffb088);
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.pageInfo {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--ifm-color-emphasis-700);
  min-width: 120px;
  text-align: center;
}

.gpuTypeButton,
.selectedGPU,
.selected {
  display: none;
}

.gpuSection {
  margin-left: 1rem;
  margin-bottom: 0.5rem;
}

.gpuName {
  margin-bottom: 0.25rem;
}

.submissions {
  margin-left: 1rem;
  font-family: var(--ifm-font-family-monospace);
}

.noSubmissions {
  color: var(--ifm-color-secondary);
  font-style: italic;
}

.timings {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.timing {
  font-family: var(--ifm-font-family-monospace);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  background: var(--ifm-background-color);
}

.first {
  border-left: 4px solid #09ff53; /* Gold */
}

.second {
  border-left: 4px solid #14ddfc; /* Silver */
}

.third {
  border-left: 4px solid #e91e1b; /* Bronze */
}

.leaderboardHeader {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.leaderboardHeader h2 {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: #ff6b00;
  text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;
  letter-spacing: -0.5px;
}
