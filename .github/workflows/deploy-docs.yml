# Workflow that builds and deploys the documentation website
# Adapted from https://github.com/All-Hands-AI/OpenHands/blob/main/.github/workflows/deploy-docs.yml
name: Deploy Docs to GitHub Pages

on:
  schedule:
    - cron: '*/10 * * * *'  # Runs at 00:00 UTC daily
  push:
    branches:
      - main  # only run on main branch pushes
  pull_request:
    paths:
      - 'docs/**'
      - '.github/workflows/deploy-docs.yml'
    branches:
      - main
  workflow_dispatch:

# If triggered by a PR, it will be in the same group. However, each commit on main will be in its own unique group
concurrency:
  group: ${{ github.workflow }}-${{ (github.head_ref && github.ref) || github.run_id }}
  cancel-in-progress: true

jobs:
  # Build job
  build:
    name: Build Docs
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      # Update Leaderboard
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install psycopg2-binary jinja2 requests
          
      - name: Update leaderboard
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          DISCORD_DUMMY_TOKEN: ${{ secrets.DISCORD_DUMMY_TOKEN }}
        run: python scripts/update_leaderboard.py

      # Build Docs
      - name: Set up node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: yarn
          cache-dependency-path: docs/yarn.lock

      - name: Install dependencies
        working-directory: docs
        run: yarn install --frozen-lockfile --non-interactive

      - name: Build docs
        working-directory: docs
        run: yarn build

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: docs/build

  # Deploy job
  deploy:
    needs: build
    runs-on: ubuntu-latest
    
    # Only deploy from main branch
    if: github.ref == 'refs/heads/main'
    
    # Grant GITHUB_TOKEN the permissions required to make a Pages deployment
    permissions:
      pages: write      # to deploy to Pages
      id-token: write   # to verify the deployment originates from an appropriate source
    
    # Deploy to the github-pages environment
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
      
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
