name: release

on:
  push:
    # Sequence of patterns matched against refs/tags
    tags:
      - 'v*' # Push events to matching v*, i.e. v1.0, v20.15.10
  
  # Keep pull request builds for testing
  pull_request:
  workflow_dispatch:

permissions:
  contents: write

jobs:
  build:
    name: Build
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            artifact_name: popcorn-cli
            asset_name: popcorn-cli-linux.tar.gz
            compress_cmd: tar -czf
            compress_ext: .tar.gz
          
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            artifact_name: popcorn-cli
            asset_name: popcorn-cli-windows.zip
            compress_cmd: 7z a
            compress_ext: .zip
          
          - os: macos-latest
            target: aarch64-apple-darwin
            artifact_name: popcorn-cli
            asset_name: popcorn-cli-macos.tar.gz
            compress_cmd: tar -czf
            compress_ext: .tar.gz

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Rust toolchain
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: stable
          targets: ${{ matrix.target }}
      
      - name: Set up cargo cache
        uses: Swatinem/rust-cache@v2
        with:
          key: ${{ matrix.target }}
      
      - name: Install cross-compilation dependencies (Linux ARM)
        if: matrix.target == 'aarch64-unknown-linux-gnu'
        run: |
          sudo apt-get update
          sudo apt-get install -y gcc-aarch64-linux-gnu
      
      - name: Build release binary
        run: cargo build --release --target ${{ matrix.target }}
      
      - name: Prepare artifact
        shell: bash
        run: |
          mkdir -p dist
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            cp target/${{ matrix.target }}/release/popcorn-cli.exe dist/popcorn-cli.exe
          else
            cp target/${{ matrix.target }}/release/popcorn-cli dist/popcorn-cli
            chmod +x dist/popcorn-cli
          fi
          cd dist
          ${{ matrix.compress_cmd }} ../${{ matrix.asset_name }} *
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.asset_name }}
          path: ${{ matrix.asset_name }}
          retention-days: 7
  
  release:
    name: Create Release
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        
      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            popcorn-cli-linux.tar.gz/popcorn-cli-linux.tar.gz
            popcorn-cli-windows.zip/popcorn-cli-windows.zip
            popcorn-cli-macos.tar.gz/popcorn-cli-macos.tar.gz
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
