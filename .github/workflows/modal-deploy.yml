name: CI/CD

on:
  push:
    branches:
      - main
      - dev

jobs:
  deploy:
    name: Modal Deployment
    runs-on: ubuntu-latest
    environment: ${{ github.ref == 'refs/heads/dev' && 'kernelbotdev' || '' }}
    env:
      MODAL_TOKEN_ID: ${{ secrets.MODAL_TOKEN_ID }}
      MODAL_TOKEN_SECRET: ${{ secrets.MODAL_TOKEN_SECRET }}

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Install Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install Modal
        run: |
          python -m pip install --upgrade pip
          pip install modal

      - name: Deploy job
        run: |
          modal deploy src/discord-cluster-manager/modal_runner_archs.py
