name: Runner CI

on:
  push:
    branches:
      - main
      - dev
  pull_request:
    branches:
      - main
      - dev

jobs:
  check-cuda:
    runs-on: [gpumode-nvidia-arc]
    timeout-minutes: 10
    container:
      image: nvidia/cuda:12.4.0-devel-ubuntu22.04
    steps:
      - uses: actions/checkout@v3

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Install pytest
        shell: bash
        run: pip install pytest

      - name: Run script
        shell: bash
        run: pytest scripts/ci_test_cuda.py

    env:
      CUDA_VISIBLE_DEVICES: 0

  check-pytorch:
    runs-on: [gpumode-nvidia-arc]
    timeout-minutes: 10
    container:
      image: nvidia/cuda:12.4.0-devel-ubuntu22.04
    steps:
      - uses: actions/checkout@v3

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          version: "latest"

      - name: Setup Python environment
        run: |
          uv venv .venv
          echo "VIRTUAL_ENV=$PWD/.venv" >> $GITHUB_ENV
          echo "$PWD/.venv/bin" >> $GITHUB_PATH
          uv pip install numpy torch setuptools ninja pytest

      - name: Run script
        shell: bash
        run: pytest scripts/ci_test_python.py

    env:
      CUDA_VISIBLE_DEVICES: 0

