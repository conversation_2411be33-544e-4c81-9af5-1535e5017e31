# name of the task
# name: identity-cuda

# these files will be baked into the json object, so that they are available during testing
files:
  - {"name": "eval.cu", "source": "../eval.cu"}
  - {"name": "task.h", "source": "task.h"}
  - {"name": "utils.h", "source": "../utils.h"}
  - {"name": "reference.cuh", "source": "reference.cuh"}
  - {"name": "submission.cu", "source": "@SUBMISSION@"}

# task language, depending on this we do get different keys in runner
lang: "cu"

description:
  A simple test task

# Config object
config:
  # task provided source files to compile
  sources: ["eval.cu", "submission.cu"]

  # additional include directories
  include_dirs: []

templates:
  CUDA: "../template.cu"

# small test cases. should be cheap to run.
tests:
  - {"size": 127, "seed": 4242}
  - {"size": 128, "seed": 5236}
  - {"size": 129, "seed": 1001}
  - {"size": 256, "seed": 5531}
  - {"size": 512, "seed": 9173}

benchmarks:
  - {"size": 1024, "seed": 54352}
  - {"size": 2048, "seed": 93246}
  - {"size": 4096, "seed": 6256}
  - {"size": 8192, "seed": 8841}
  - {"size": 16384, "seed": 6252}
  - {"size": 32768, "seed": 52624}
  - {"size": 65536, "seed": 125432}
