import dataclasses
import datetime
import functools
import os
import shlex
import subprocess
import tempfile
import time
from pathlib import Path
from types import NoneType
from typing import Optional, Protocol, Union

from consts import CUDA_FLAGS, ExitCode, Timeout


@dataclasses.dataclass
class CompileResult:
    # fmt: off
    nvcc_found: bool    # did we find nvcc?
    nvcc_version: str   # the result of nvcc --version
    success: bool       # did it compile successfully
    command: str        # the command that was run to compile the code
    stdout: str         # standard output produced by the compiler
    stderr: str         # standard error produced by the compiler
    exit_code: int      # exit code produced by the compiler
    # fmt: on


@dataclasses.dataclass
class RunResult:
    # fmt: off
    success: bool       # did the compiled executable run successfully
    passed: bool        # did it pass all tests
    command: str        # the command that was run to compile the code
    stdout: str         # standard output produced by the compiler
    stderr: str         # standard error produced by the compiler
    exit_code: int      # exit code produced by the compiler
    duration: float     # execution time (NOT kernel duration)
    result: dict        # dictionary with the results generated by the tester
    # fmt: on


@dataclasses.dataclass
class SystemInfo:
    # fmt: off
    gpu: str = ''           # Model name of the GPU
    cpu: str = ''           # Model name of the CPU
    platform: str = ''      # Platform string of the machine
    torch: str = ''         # Torch version
    # fmt: on


@dataclasses.dataclass
class EvalResult:
    # fmt: off
    start: datetime.datetime            # when did this run start (excluding container setup time)
    end: datetime.datetime              # and when did it finish
    compilation: CompileResult | None   # results of compilation
    run: RunResult | None               # result of actually running the executable/script
    # fmt: on


@dataclasses.dataclass
class FullResult:
    # fmt: off
    success: bool                  # did the runner (github/modal) execute successfully
    error: str                     # if not success, an error message
    system: SystemInfo             # specs of the system this was run on
    # results of running. There can be multiple runs in one submission, using separate
    # 'test' and 'benchmark' keys, for example
    runs: dict[str, EvalResult] = dataclasses.field(default_factory=dict)
    # fmt: on


def _make_cmd(args: list[str]):
    return " ".join(map(shlex.quote, args))


def _limit_length(text: Union[NoneType, str, bytes], max_len: int = 16384):
    if text is None:
        return ""
    if isinstance(text, bytes):
        text = text.decode("utf-8")
    lines = text.split("\n")
    size = 0
    for i, line in enumerate(lines):
        size += len(line) + 1
        if size + 100 > max_len:
            lines = lines[:i] + [f"[...] {len(lines) - i} lines omitted"]
            return "\n".join(lines)
    return text


def _create_files(files: Optional[dict[str, str]]):
    """
    Create text files
    Args:
        files: A dictionary mapping file names to their contents.
    Raises:
        AssertionError, if the file is not within the current working directory.
    """
    if files is None:
        return

    for name, content in files.items():
        assert Path(name).resolve().is_relative_to(Path.cwd())
        Path(name).write_text(content)


def compile_cuda_script(  # # noqa: C901
    files: list[str],
    arch: Optional[int] = None,
    include_dirs: Optional[list[str]] = None,
    defines: Optional[dict[str, str]] = None,
    libraries: Optional[list[str]] = None,
    flags: Optional[list[str]] = None,
    verbose: bool = False,
) -> CompileResult:
    """
    Compiles a set of cuda files with nvcc.

    Args:
        files: List of files to compile.
        arch: Architecture to compile for. If None, uses `native`
        include_dirs: additional include directories to supply to nvcc
        defines: Additional defines for the preprocessor
        libraries: Additional libraries to link to
        flags: Other compiler flags
        verbose: whether to print progress or be silent
    Returns:
        A `CompileResult` that summarizes the compilation process.

    """
    if flags is None:
        flags = CUDA_FLAGS

    if include_dirs is not None:
        flags += [f"-I{d}" for d in include_dirs]
        # validate include directories
        for directory in include_dirs:
            if not Path(directory).exists():
                raise FileNotFoundError(f"Directory `{directory}` does not exist")
            elif not Path(directory).is_dir():
                raise NotADirectoryError(f"`{directory}` is not a directory")

    if libraries is not None:
        flags += [f"-l{lib}" for lib in libraries]

    if defines is not None:
        for name, value in defines.items():
            # restrict macro names to valid identifiers
            if not name.isidentifier():
                raise ValueError(f"Define key `{name}` contains invalid character")

            if value is not None:
                flags.append(f"-D{name}={value}")
            else:
                flags.append(f"-D{name}")

    for flag in flags:
        if not flag.startswith("-"):
            raise ValueError(f"Flag `{flag}` should start with a dash.")

    if verbose:
        print_ = print
    else:
        print_ = lambda *args, **kwargs: None  # noqa

    # Check CUDA is available and installed correctly
    print_("[CUDA Env Check]")
    try:
        # these check cuda compiler is also available
        nvcc = subprocess.check_output(["which", "nvcc"], encoding="utf-8").strip()
        nvcc_version = subprocess.check_output(["nvcc", "--version"], encoding="utf-8")
    except subprocess.CalledProcessError as e:
        return CompileResult(
            nvcc_found=False,
            success=False,
            nvcc_version="",
            command=_make_cmd(e.cmd),
            stdout=_limit_length(e.stdout),
            stderr=_limit_length(e.stderr),
            exit_code=e.returncode,
        )

    if arch is None:
        ARCH = "-arch=native"
    else:
        ARCH = f"-gencode=arch=compute_{arch},code=sm_{arch}"

    command = [nvcc] + flags + files + [ARCH, "-o", "eval.out"]

    print_("[Compiling]")
    try:
        compile_process = subprocess.run(
            command, capture_output=True, text=True, check=True, timeout=Timeout.COMPILE
        )
    except subprocess.CalledProcessError as e:
        return CompileResult(
            nvcc_found=True,
            success=False,
            nvcc_version=nvcc_version,
            command=_make_cmd(e.cmd),
            stdout=_limit_length(e.stdout),
            stderr=_limit_length(e.stderr),
            exit_code=e.returncode,
        )

    return CompileResult(
        nvcc_found=True,
        success=True,
        nvcc_version=nvcc_version,
        command=_make_cmd(compile_process.args),
        stdout=_limit_length(compile_process.stdout),
        stderr=_limit_length(compile_process.stderr),
        exit_code=compile_process.returncode,
    )


def run_program(args: list[str], seed: Optional[int], timeout: int) -> RunResult:
    print("[Running]")
    # set up a pipe so the tester can communicate its verdict with us
    env = os.environ.copy()
    pipe_read, pipe_write = os.pipe()
    env["POPCORN_FD"] = str(pipe_write)
    if seed is not None:
        env["POPCORN_SEED"] = str(seed)

    execution_start_time = time.perf_counter()
    try:
        run_process = subprocess.run(
            args,
            capture_output=True,
            text=True,
            check=False,
            env=env,
            pass_fds=[pipe_write],
            timeout=timeout,
        )
    except subprocess.TimeoutExpired as e:
        return RunResult(
            success=False,
            passed=False,
            command=_make_cmd(e.cmd),
            stdout=_limit_length(e.stdout),
            stderr=_limit_length(e.stderr),
            exit_code=ExitCode.TIMEOUT_EXPIRED,
            duration=timeout,
            result={},
        )
    execution_end_time = time.perf_counter()

    # terminate output writing
    os.close(pipe_write)
    # and fetch pipe's content
    result = os.fdopen(pipe_read, "r").read()

    result_dict = {}
    for line in result.splitlines():
        key, _, value = line.partition(":")
        if key != "" or value != "":
            result_dict[key.strip()] = value.strip()

    return RunResult(
        success=(
            run_process.returncode == ExitCode.SUCCESS
            or run_process.returncode == ExitCode.VALIDATE_FAIL
        ),
        passed=result_dict.get("check", None) == "pass",
        command=_make_cmd(run_process.args),
        stdout=_limit_length(run_process.stdout),
        stderr=_limit_length(run_process.stderr),
        exit_code=run_process.returncode,
        duration=execution_end_time - execution_start_time,
        result=result_dict,
    )


def run_single_evaluation(
    call: list[str],
    mode: str,
    tests: Optional[str] = None,
    benchmarks: Optional[str] = None,
    test_timeout: int = Timeout.TEST,
    benchmark_timeout: int = Timeout.BENCHMARK,
    ranked_timeout: int = Timeout.RANKED,
    ranking_by: str = "last",
    seed: Optional[int] = None,
) -> RunResult:
    """
    A single runner run, either in the context of test files, or in the
    context of benchmark files.
    """
    if mode == "test":
        with tempfile.NamedTemporaryFile("w") as tests_file:
            tests_file.write(tests)
            tests_file.flush()
            return run_program(call + [mode, tests_file.name], seed=seed, timeout=test_timeout)
    elif mode in ["benchmark", "profile", "leaderboard"]:
        timeout = ranked_timeout if mode == "leaderboard" else benchmark_timeout
        with tempfile.NamedTemporaryFile("w") as bench_file:
            if ranking_by == "last":
                bench_file.write(benchmarks.splitlines(keepends=True)[-1])
            else:
                bench_file.write(benchmarks)
            bench_file.flush()
            return run_program(call + [mode, bench_file.name], seed=seed, timeout=timeout)
    else:
        raise ValueError(f"Invalid mode {mode}")


def make_system_info() -> SystemInfo:
    info = SystemInfo()
    try:
        import torch

        info.torch = torch.torch_version.internal_version
        # Note: cuda.is_available() also covers HiP
        # https://pytorch.org/docs/stable/notes/hip.html
        if torch.cuda.is_available():
            info.gpu = torch.cuda.get_device_name()
    except ImportError:
        # get GPU info manually
        try:
            info.gpu = subprocess.check_output(
                ["nvidia-smi", "--query-gpu=name", "--format=csv,noheader"], encoding="utf-8"
            )
        except subprocess.CalledProcessError:
            # try again for HIP
            # TODO suggested by Claude, untested
            try:
                info.gpu = subprocess.check_output(
                    ["rocm-smi", "--showproductname"], encoding="utf-8"
                )
            except subprocess.CalledProcessError:
                # OK, no GPU info available
                pass

    try:
        cpu_info_str = Path("/proc/cpuinfo").read_text()
        cpu_info_dict = {}
        for line in cpu_info_str.splitlines():
            key, _, val = line.partition(":")
            cpu_info_dict[key.strip()] = val.strip()
        info.cpu = cpu_info_dict.get("model name", "")
        # on modal, we don't get to know the exact CPU model
        # make due with the vendor in that case
        if info.cpu == "unknown":
            # ¯\_(ツ)_/¯
            info.cpu = cpu_info_dict.get("vendor_id", "")

    except PermissionError:
        # nothing we can do here; we're not getting CPU info
        pass
    import platform

    info.platform = platform.platform()

    return info


def run_cuda_script(  # # noqa: C901
    sources: dict[str, str],
    headers: Optional[dict[str, str]] = None,
    arch: Optional[int] = None,
    defines: Optional[dict[str, str]] = None,
    include_dirs: Optional[list[str]] = None,
    libraries: Optional[list[str]] = None,
    flags: Optional[list[str]] = None,
    **kwargs,
) -> EvalResult:
    """
    Executes the provided CUDA kernel in an isolated environment

    Args:
        sources: The source files to compile. Mapping file name to content.
        headers: Additional header files to create for the compile run.
            Mapping of file name to file contents. These files will _not_ be added to the
            compile command.
        arch: The arch code for the compute/sm versions. If None, native arch is used.
        include_dirs: Additional include directories, e.g., for thunderkittens/cutlass etc
        defines: Preprocessor defines
        libraries: Additional libraries to link to
        flags: Additional flags to give to the compiler
        seed: Random seed to initialize the RNG for testing

    Returns:
        tuple[CompileResult, RunResult]: CUDA compile/eval result information
    """
    start = datetime.datetime.now()
    try:
        # Write submission files to directory
        _create_files(sources)
        _create_files(headers)

        compile_result = compile_cuda_script(
            files=list(sources.keys()),
            arch=arch,
            include_dirs=include_dirs,
            defines=defines,
            libraries=libraries,
            flags=flags,
            verbose=True,
        )

        if not compile_result.success:
            return EvalResult(
                start=start,
                end=datetime.datetime.now(),
                compilation=compile_result,
                run=None,
            )

    # cleaning up all source files _before_ we let the user code run, just in
    # case there's something in there that the user isn't supposed to snoop
    finally:
        tmp_files = list(sources.keys()) + list((headers or {}).keys())
        for f in tmp_files:
            if os.path.exists(f):
                os.remove(f)

    run_result = run_single_evaluation(["./eval.out"], **kwargs)
    return EvalResult(
        start=start,
        end=datetime.datetime.now(),
        compilation=compile_result,
        run=run_result,
    )


def run_pytorch_script(  # noqa: C901
    sources: dict[str, str],
    main: str,
    **kwargs,
) -> EvalResult:
    """
    Executes the provided PyTorch GPU kernel in an isolated environment

    Args:
        sources: Files to generate
        main: Which file to run. Must be one of the keys in sources.
        seed: Random seed to initialize the RNG for testing

    Returns:
        RunResult
    """
    start = datetime.datetime.now()
    try:
        assert main in sources.keys()

        # Write submission files to directory
        _create_files(sources)

        # "compile" step: execute the script once. Will populate
        # `load_inline`'s compile cache, so the actual runs will be faster.
        try:
            compile_run = run_program(["python", "submission.py"], seed=1, timeout=Timeout.COMPILE)
            if "-DTORCH_EXTENSION_NAME" in compile_run.stdout:
                comp = CompileResult(
                    nvcc_found=True,
                    nvcc_version="",
                    success=True,
                    command=compile_run.command,
                    stdout=compile_run.stdout,
                    stderr=compile_run.stderr,
                    exit_code=compile_run.exit_code,
                )
            else:
                comp = None
        except subprocess.CalledProcessError as e:
            # This step is purely optional, so we just go on
            # if it fails
            comp = CompileResult(
                nvcc_found=False,
                nvcc_version="",
                success=False,
                command="python submission.py",
                stdout=e.stdout,
                stderr=e.stderr,
                exit_code=e.returncode,
            )

        run = run_single_evaluation(["python", main], **kwargs)

        return EvalResult(
            start=start,
            end=datetime.datetime.now(),
            compilation=comp,
            run=run,
        )
    finally:
        for f in sources.keys():
            if os.path.exists(f):
                os.remove(f)


class _EvalRunner(Protocol):
    def __call__(self, mode: str) -> EvalResult: ...


def run_evaluation(
    call: _EvalRunner,
    mode: str,
) -> dict[str, EvalResult]:
    """
    Given a "runner" function `call`, interprets the mode
    and calls the runner with the right arguments.
    Simple modes (test, benchmark, profile) just
    invoke the runner once, but private/leaderboard
    require multiple runner calls.
    """
    results: dict[str, EvalResult] = {}
    if mode in ["test", "benchmark", "profile"]:
        results[mode] = call(mode=mode)
    elif mode in ["private", "leaderboard"]:
        # first, run the tests
        results["test"] = call(mode="test")

        if not results["test"].run or not results["test"].run.passed:
            return results

        results["benchmark"] = call(mode="benchmark")

        if not results["benchmark"].run or not results["benchmark"].run.passed:
            return results

        # if they pass, run the leaderboard validation
        results["leaderboard"] = call(mode="leaderboard")
    else:
        raise AssertionError("Invalid mode")

    return results


def build_test_string(tests: list[dict]):
    as_str = ""
    for test in tests:
        kvs = []
        for k, v in test.items():
            kvs.append(f"{k}: {v}")
        as_str += "; ".join(kvs) + "\n"
    return as_str


def run_config(config: dict):
    common_args = {
        "tests": build_test_string(config.get("tests", [])),
        "benchmarks": build_test_string(config.get("benchmarks", [])),
        "seed": config.get("seed", None),
        "ranking_by": config.get("ranking_by", "last"),
        "ranked_timeout": config.get("ranked_timeout", Timeout.RANKED),
        "benchmark_timeout": config.get("benchmark_timeout", Timeout.BENCHMARK),
        "test_timeout": config.get("test_timeout", Timeout.TEST),
    }
    if config["lang"] == "py":
        runner = functools.partial(
            run_pytorch_script,
            sources=config["sources"],
            main=config["main"],
            **common_args,
        )
    elif config["lang"] == "cu":
        runner = functools.partial(
            run_cuda_script,
            sources=config["sources"],
            headers=config.get("headers", {}),
            arch=config.get("arch", None),
            defines=config.get("defines", {}),
            include_dirs=config.get("include_dirs", []),
            libraries=config.get("libraries", []),
            flags=CUDA_FLAGS,
            **common_args,
        )
    else:
        raise ValueError(f"Invalid language {config['lang']}")

    results = run_evaluation(runner, config["mode"])
    return FullResult(success=True, error="", runs=results, system=make_system_info())
