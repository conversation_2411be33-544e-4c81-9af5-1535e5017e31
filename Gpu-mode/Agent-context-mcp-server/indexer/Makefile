# RAG Indexer Development Makefile

.PHONY: help install install-dev test test-fast lint format type-check clean build docs

# Default target
help:
	@echo "RAG Indexer Development Commands:"
	@echo ""
	@echo "Setup:"
	@echo "  install      Install production dependencies"
	@echo "  install-dev  Install development dependencies"
	@echo ""
	@echo "Development:"
	@echo "  test         Run full test suite with coverage"
	@echo "  test-fast    Run tests without coverage"
	@echo "  lint         Run linting checks"
	@echo "  format       Format code with black and isort"
	@echo "  type-check   Run mypy type checking"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean        Clean build artifacts and cache"
	@echo "  build        Build package"
	@echo "  docs         Generate documentation"

# Installation targets
install:
	pip install -r requirements.txt
	pip install -e .

install-dev:
	pip install -r requirements-dev.txt
	pip install -e .

# Testing targets
test:
	pytest tests/ --cov=indexer --cov-report=term-missing --cov-report=html --cov-fail-under=80 -v

test-fast:
	pytest tests/ -v

# Code quality targets
lint:
	flake8 indexer/ tests/
	isort --check-only indexer/ tests/

format:
	black indexer/ tests/
	isort indexer/ tests/

type-check:
	mypy indexer/

# Maintenance targets
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build:
	python -m build

docs:
	cd docs && make html

# Development workflow
dev-setup: install-dev
	pre-commit install

# Run all quality checks
check: lint type-check test

# Quick development cycle
dev: format lint type-check test-fast
