"""
Vector Embedding Generation

This module handles the generation of vector embeddings from code chunks.
It supports multiple embedding models and provides batch processing for
efficiency.

Key classes:
- EmbeddingModel: Abstract base for embedding models
- SentenceTransformerModel: Implementation using sentence-transformers
- EmbeddingPipeline: Main pipeline for processing chunks
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
import logging
import numpy as np

from .chunking import CodeChunk

logger = logging.getLogger(__name__)


class EmbeddingModel(ABC):
    """Abstract base class for embedding models."""
    
    @abstractmethod
    def encode(self, texts: List[str], batch_size: int = 32) -> np.ndarray:
        """
        Encode texts into embeddings.
        
        Args:
            texts: List of texts to encode
            batch_size: Batch size for processing
            
        Returns:
            Array of embeddings
        """
        pass
    
    @abstractmethod
    def get_dimension(self) -> int:
        """Get embedding dimension."""
        pass
    
    @abstractmethod
    def get_model_name(self) -> str:
        """Get model name."""
        pass


class SentenceTransformerModel(EmbeddingModel):
    """Embedding model using sentence-transformers."""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2", device: str = "cpu"):
        """
        Initialize sentence transformer model.
        
        Args:
            model_name: Name of the sentence transformer model
            device: Device to run on ('cpu' or 'cuda')
        """
        self.model_name = model_name
        self.device = device
        self._model = None
        
    def _load_model(self):
        """Lazy load the model."""
        if self._model is None:
            try:
                from sentence_transformers import SentenceTransformer
                self._model = SentenceTransformer(self.model_name, device=self.device)
                logger.info(f"Loaded embedding model: {self.model_name}")
            except ImportError:
                raise ImportError("sentence-transformers not installed. Run: pip install sentence-transformers")
    
    def encode(self, texts: List[str], batch_size: int = 32) -> np.ndarray:
        """Encode texts using sentence transformer."""
        self._load_model()
        
        logger.debug(f"Encoding {len(texts)} texts with batch size {batch_size}")
        embeddings = self._model.encode(
            texts,
            batch_size=batch_size,
            show_progress_bar=len(texts) > 100,
            convert_to_numpy=True
        )
        
        return embeddings
    
    def get_dimension(self) -> int:
        """Get embedding dimension."""
        self._load_model()
        return self._model.get_sentence_embedding_dimension()
    
    def get_model_name(self) -> str:
        """Get model name."""
        return self.model_name


class OpenAIEmbeddingModel(EmbeddingModel):
    """Embedding model using OpenAI API."""
    
    def __init__(self, model_name: str = "text-embedding-ada-002", api_key: Optional[str] = None):
        """
        Initialize OpenAI embedding model.
        
        Args:
            model_name: OpenAI model name
            api_key: OpenAI API key (if None, will use environment variable)
        """
        self.model_name = model_name
        self.api_key = api_key
        self._client = None
        
    def _load_client(self):
        """Lazy load OpenAI client."""
        if self._client is None:
            try:
                import openai
                self._client = openai.OpenAI(api_key=self.api_key)
                logger.info(f"Initialized OpenAI client for model: {self.model_name}")
            except ImportError:
                raise ImportError("openai not installed. Run: pip install openai")
    
    def encode(self, texts: List[str], batch_size: int = 32) -> np.ndarray:
        """Encode texts using OpenAI API."""
        self._load_client()
        
        # TODO: Implement OpenAI embedding with rate limiting and batching
        logger.warning("OpenAI embedding not yet implemented")
        # Return dummy embeddings for now
        return np.random.rand(len(texts), 1536)
    
    def get_dimension(self) -> int:
        """Get embedding dimension."""
        return 1536  # ada-002 dimension
    
    def get_model_name(self) -> str:
        """Get model name."""
        return self.model_name


class EmbeddingPipeline:
    """Pipeline for processing chunks and generating embeddings."""
    
    def __init__(self, model: EmbeddingModel, batch_size: int = 32):
        """
        Initialize embedding pipeline.
        
        Args:
            model: Embedding model to use
            batch_size: Batch size for processing
        """
        self.model = model
        self.batch_size = batch_size
        
    def process_chunks(self, chunks: List[CodeChunk]) -> List[Dict[str, Any]]:
        """
        Process chunks and generate embeddings.
        
        Args:
            chunks: List of code chunks to process
            
        Returns:
            List of processed chunk data with embeddings
        """
        if not chunks:
            return []
            
        logger.info(f"Processing {len(chunks)} chunks for embedding")
        
        # Prepare texts for embedding
        texts = [chunk.to_embedding_text() for chunk in chunks]
        
        # Generate embeddings
        embeddings = self.model.encode(texts, batch_size=self.batch_size)
        
        # Prepare results
        results = []
        for chunk, embedding in zip(chunks, embeddings):
            result = {
                'id': chunk.get_unique_id(),
                'content': chunk.content,
                'embedding': embedding.tolist(),
                'metadata': {
                    'file_path': chunk.file_path,
                    'start_line': chunk.start_line,
                    'end_line': chunk.end_line,
                    'chunk_type': chunk.chunk_type,
                    'language': chunk.language,
                    **chunk.metadata
                }
            }
            results.append(result)
            
        logger.info(f"Generated embeddings for {len(results)} chunks")
        return results
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the embedding model."""
        return {
            'model_name': self.model.get_model_name(),
            'dimension': self.model.get_dimension(),
            'batch_size': self.batch_size
        }


def create_embedding_model(model_name: str, device: str = "cpu", **kwargs) -> EmbeddingModel:
    """
    Factory function to create embedding models.
    
    Args:
        model_name: Name of the model
        device: Device to run on
        **kwargs: Additional model-specific arguments
        
    Returns:
        Embedding model instance
    """
    if model_name.startswith("text-embedding-"):
        # OpenAI model
        return OpenAIEmbeddingModel(model_name, **kwargs)
    else:
        # Sentence transformer model
        return SentenceTransformerModel(model_name, device, **kwargs)
