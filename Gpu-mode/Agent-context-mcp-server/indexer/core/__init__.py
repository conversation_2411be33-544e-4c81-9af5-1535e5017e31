"""
Core Indexer Functionality

This package contains the core components of the RAG indexer:
- traversal: File discovery and filtering
- chunking: Code parsing and semantic chunking
- embedding: Vector generation from code chunks
- storage: Database operations for vector storage
- indexer: Main orchestrator class
"""

from .chunking import CodeChunk, ChunkMetadata
from .indexer import CodebaseIndexer

__all__ = ["CodeChunk", "ChunkMetadata", "CodebaseIndexer"]
