"""
Main Indexer Orchestrator

This module contains the main CodebaseIndexer class that orchestrates
the entire indexing process from file discovery to vector storage.

Key classes:
- CodebaseIndexer: Main orchestrator for the indexing process
"""

from pathlib import Path
from typing import List, Dict, Any, Optional
import logging
from tqdm import tqdm

from .traversal import discover_files, get_file_type
from .chunking import Chunking<PERSON>ng<PERSON>, CodeChunk
from .embedding import Embedding<PERSON>ipeline, create_embedding_model
from .storage import StorageManager, create_vector_store
from ..config.settings import IndexConfig

logger = logging.getLogger(__name__)


class CodebaseIndexer:
    """Main orchestrator for codebase indexing."""
    
    def __init__(self, config: IndexConfig):
        """
        Initialize codebase indexer.
        
        Args:
            config: Indexing configuration
        """
        self.config = config
        
        # Initialize components
        self.chunking_engine = ChunkingEngine(
            max_chunk_size=config.max_chunk_size,
            overlap_size=config.overlap_size
        )
        
        self.embedding_model = create_embedding_model(
            model_name=config.embedding_model,
            device=config.device
        )
        
        self.embedding_pipeline = EmbeddingPipeline(
            model=self.embedding_model,
            batch_size=config.batch_size
        )
        
        self.vector_store = create_vector_store(
            store_type="chroma",
            db_path=str(config.db_path)
        )
        
        self.storage_manager = StorageManager(
            vector_store=self.vector_store,
            collection_name=config.collection_name
        )
        
        # Statistics
        self.stats = {
            'files_processed': 0,
            'files_failed': 0,
            'chunks_generated': 0,
            'chunks_stored': 0,
            'total_files': 0
        }
    
    def index_repository(self) -> Dict[str, Any]:
        """
        Main entry point for indexing a repository.
        
        Returns:
            Dictionary with indexing statistics and results
        """
        logger.info(f"Starting indexing of repository: {self.config.repository_path}")
        
        try:
            # Step 1: Discover files
            files = self._discover_files()
            self.stats['total_files'] = len(files)
            
            if not files:
                logger.warning("No files found to index")
                return self.stats
            
            # Step 2: Initialize storage
            model_info = self.embedding_pipeline.get_model_info()
            self.storage_manager.initialize_storage(model_info)
            
            # Step 3: Process files and generate chunks
            all_chunks = self._process_files(files)
            self.stats['chunks_generated'] = len(all_chunks)
            
            if not all_chunks:
                logger.warning("No chunks generated from files")
                return self.stats
            
            # Step 4: Generate embeddings and store
            self._store_chunks(all_chunks)
            
            logger.info("Indexing completed successfully")
            logger.info(f"Statistics: {self.stats}")
            
            return self.stats
            
        except Exception as e:
            logger.error(f"Error during indexing: {e}")
            raise
    
    def _discover_files(self) -> List[Path]:
        """Discover files to process."""
        logger.info("Discovering files...")
        
        files = discover_files(
            repo_path=self.config.repository_path,
            ignore_patterns=self.config.ignore_patterns,
            include_extensions=set(self.config.include_extensions) if self.config.include_extensions else None,
            max_file_size_mb=self.config.max_file_size_mb
        )
        
        logger.info(f"Found {len(files)} files to process")
        return files
    
    def _process_files(self, files: List[Path]) -> List[CodeChunk]:
        """Process files and generate chunks."""
        logger.info("Processing files and generating chunks...")
        
        all_chunks = []
        
        # Use tqdm for progress tracking
        for file_path in tqdm(files, desc="Processing files"):
            try:
                chunks = self._process_single_file(file_path)
                all_chunks.extend(chunks)
                self.stats['files_processed'] += 1
                
                if len(chunks) > 0:
                    logger.debug(f"Processed {file_path}: {len(chunks)} chunks")
                    
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}")
                self.stats['files_failed'] += 1
                continue
        
        logger.info(f"Generated {len(all_chunks)} total chunks from {self.stats['files_processed']} files")
        return all_chunks
    
    def _process_single_file(self, file_path: Path) -> List[CodeChunk]:
        """Process a single file and return chunks."""
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Skip empty files
            if not content.strip():
                logger.debug(f"Skipping empty file: {file_path}")
                return []
            
            # Determine file type
            file_type = get_file_type(file_path)
            
            # Generate chunks
            chunks = self.chunking_engine.chunk_file(file_path, content, file_type)
            
            return chunks
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            return []
    
    def _store_chunks(self, chunks: List[CodeChunk]) -> None:
        """Generate embeddings and store chunks."""
        logger.info("Generating embeddings and storing chunks...")
        
        # Process chunks in batches to manage memory
        batch_size = self.config.batch_size * 4  # Larger batches for storage
        
        for i in tqdm(range(0, len(chunks), batch_size), desc="Storing chunks"):
            batch = chunks[i:i + batch_size]
            
            try:
                # Generate embeddings
                processed_chunks = self.embedding_pipeline.process_chunks(batch)
                
                # Store in vector database
                self.storage_manager.store_chunks(processed_chunks)
                
                self.stats['chunks_stored'] += len(processed_chunks)
                
            except Exception as e:
                logger.error(f"Error storing chunk batch {i//batch_size + 1}: {e}")
                continue
        
        logger.info(f"Stored {self.stats['chunks_stored']} chunks in vector database")
    
    def update_repository(self, incremental: bool = True) -> Dict[str, Any]:
        """
        Update an existing index.
        
        Args:
            incremental: Whether to perform incremental update
            
        Returns:
            Update statistics
        """
        # TODO: Implement incremental updates
        logger.warning("Incremental updates not yet implemented")
        
        if incremental:
            # For now, just re-index everything
            logger.info("Performing full re-index (incremental not implemented)")
            return self.index_repository()
        else:
            return self.index_repository()
    
    def query_index(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Query the index for testing.
        
        Args:
            query: Search query
            limit: Number of results to return
            
        Returns:
            List of search results
        """
        logger.info(f"Querying index: {query}")
        
        # Generate query embedding
        query_embedding = self.embedding_model.encode([query])[0]
        
        # Search for similar chunks
        results = self.storage_manager.search_similar(
            query_embedding.tolist(),
            limit=limit
        )
        
        logger.info(f"Found {len(results)} results")
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get indexing and storage statistics."""
        storage_stats = self.storage_manager.get_stats()
        
        return {
            **self.stats,
            'storage_info': storage_stats,
            'model_info': self.embedding_pipeline.get_model_info()
        }
