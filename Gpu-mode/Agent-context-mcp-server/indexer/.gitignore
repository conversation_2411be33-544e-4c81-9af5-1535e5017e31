# RAG Indexer specific .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Type checking
.mypy_cache/
.dmypy.json
dmypy.json

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Development tools
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Indexer specific
# Vector databases
chroma_db/
*.db
*.sqlite
*.sqlite3

# Index outputs
index_output/
index_db/
embeddings/

# Configuration overrides
*_local.yaml
*_dev.yaml
.env
.env.local

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# Performance profiling
*.prof
*.lprof
