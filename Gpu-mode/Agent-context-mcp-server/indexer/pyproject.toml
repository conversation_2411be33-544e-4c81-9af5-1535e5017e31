[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "indexer"
version = "0.1.0"
description = "RAG Indexer Component of the Agent Context MCP Server"
readme = "INDEXER.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "ben.ho<PERSON><PERSON>"},
]
keywords = ["rag", "indexer", "semantic-search", "code-analysis", "embeddings"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Text Processing :: Indexing",
]

dependencies = [
    "tree-sitter>=0.20.4",
    "tree-sitter-python>=0.20.4",
    "tree-sitter-javascript>=0.20.3",
    "pydantic>=2.4.2",
    "pyyaml>=6.0.1",
    "click>=8.1.7",
    "tqdm>=4.66.1",
    "gitpython>=3.1.40",
]

[project.optional-dependencies]
embedding = [
    "sentence-transformers>=2.2.2",
    "chromadb>=0.4.15",
    "openai>=1.0.0",
]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]
all = [
    "indexer[embedding,dev]",
]

[project.urls]
Homepage = "https://github.com/b9r5/agent-context-mcp-server"
Repository = "https://github.com/b9r5/agent-context-mcp-server"
Issues = "https://github.com/b9r5/agent-context-mcp-server/issues"

[project.scripts]
indexer = "indexer.main:cli"

[tool.setuptools.packages.find]
where = ["."]
include = ["indexer*"]
exclude = ["tests*"]

[tool.black]
line-length = 100
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]

[tool.coverage.run]
source = ["indexer"]
omit = [
    "*/tests/*",
    "*/test_*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
