# RAG Indexer

The RAG Indexer is Component 1 of the Agent Context MCP Server system. It's an offline process that takes a codebase and prepares it for semantic search by creating vector embeddings and storing them in a searchable database.

## Project Structure

```
indexer/
├── __init__.py              # Package initialization
├── main.py                  # CLI entry point
├── core/                    # Core functionality
│   ├── __init__.py
│   ├── traversal.py         # File discovery and filtering
│   ├── chunking.py          # Code parsing and chunking
│   ├── embedding.py         # Vector generation
│   ├── storage.py           # Database operations
│   └── indexer.py           # Main orchestrator
├── parsers/                 # Language-specific parsers
│   ├── __init__.py
│   ├── base.py              # Abstract parser interface
│   ├── python.py            # Python-specific parsing
│   ├── javascript.py        # JS/TS parsing
│   ├── markdown.py          # Documentation parsing
│   └── generic.py           # Fallback parser
├── config/                  # Configuration management
│   ├── __init__.py
│   └── settings.py          # Configuration models
├── utils/                   # Utility functions
│   ├── __init__.py
│   ├── gitignore.py         # .gitignore parsing
│   └── logging.py           # Logging setup
├── requirements.txt         # Dependencies
├── pyproject.toml          # Python packaging
├── indexer_config.yaml     # Default configuration
└── README.md               # This file
```

## Installation

### Development Setup

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install in development mode:
```bash
pip install -e .
```

### Production Installation

```bash
pip install rag-indexer
```

## Usage

### Command Line Interface

```bash
# Basic indexing
python -m indexer index /path/to/repo

# With custom configuration
python -m indexer index /path/to/repo --config custom_config.yaml

# Update existing index (not yet implemented)
python -m indexer update /path/to/repo --incremental

# Query index for testing (not yet implemented)
python -m indexer query "how to authenticate users"

# Show index statistics (not yet implemented)
python -m indexer stats
```

### Configuration

Create a configuration file based on `indexer_config.yaml`:

```yaml
# Input settings
repository_path: /path/to/repo
output_path: ./index_output

# File filtering
include_extensions:
  - .py
  - .js
  - .ts
  - .md
ignore_patterns:
  - node_modules/
  - .git/
  - __pycache__/

# Chunking settings
max_chunk_size: 1000
overlap_size: 100

# Embedding settings (Phase 2)
embedding_model: all-MiniLM-L6-v2
batch_size: 32
device: cpu

# Storage settings (Phase 2)
db_path: ./chroma_db
collection_name: codebase_chunks
```

### Environment Variables

You can override configuration using environment variables:

```bash
export INDEXER_REPOSITORY_PATH=/path/to/repo
export INDEXER_EMBEDDING_MODEL=all-mpnet-base-v2
export INDEXER_DEVICE=cuda
export INDEXER_BATCH_SIZE=64
```

## Development Status

### Phase 1: Core Traversal and Chunking ✅ (Task 1 Complete)

**Task 1: Initialize Project Structure** ✅
- [x] Complete directory structure created
- [x] All package `__init__.py` files with docstrings
- [x] Placeholder modules with basic implementations
- [x] Python packaging structure (pyproject.toml)
- [x] Requirements and configuration files

**Remaining Phase 1 Tasks:**
- [ ] Task 2: Dependencies and Environment Setup
- [ ] Task 3: Core Data Models
- [ ] Task 4: Configuration System
- [ ] Task 5: Logging Infrastructure
- [ ] Task 6: .gitignore Parser
- [ ] Task 7: File Discovery Engine
- [ ] Task 8: File Type Detection and Filtering
- [ ] Task 9: Parser Infrastructure
- [ ] Task 10: Python Parser Implementation
- [ ] Task 11: JavaScript/TypeScript Parser
- [ ] Task 12: Core Chunking Logic
- [ ] Task 13: Documentation Parser
- [ ] Task 14: Generic Fallback Parser
- [ ] Task 15: Command Line Interface
- [ ] Task 16: Main Indexer Orchestrator
- [ ] Task 17: Integration Testing and Validation
- [ ] Task 18: Comprehensive Test Suite
- [ ] Task 19: Documentation and Examples

### Phase 2: Embedding Generation and Storage (Planned)
- Vector embedding generation using sentence-transformers
- ChromaDB integration for vector storage
- Batch processing and memory management

### Phase 3: Performance Optimization (Planned)
- Parallel processing
- Incremental updates
- Memory optimization

### Phase 4: Testing and Documentation (Planned)
- Comprehensive test suite
- Performance benchmarks
- API documentation

### Phase 5: MCP Server Integration (Planned)
- Integration with MCP server component
- Query interface
- Real-time updates

## Architecture

The indexer follows a modular architecture:

1. **File Discovery**: Recursively scan repositories respecting .gitignore
2. **Semantic Chunking**: Parse code using tree-sitter for semantic boundaries
3. **Embedding Generation**: Convert chunks to vector embeddings
4. **Vector Storage**: Store embeddings in ChromaDB for fast retrieval

## Supported Languages

- **Python** (.py, .pyw, .pyi) - Full tree-sitter support
- **JavaScript/TypeScript** (.js, .jsx, .ts, .tsx) - Full tree-sitter support
- **Markdown** (.md, .markdown) - Header-based chunking
- **Configuration** (.json, .yaml, .toml) - Structure-aware parsing
- **Generic** (all other text files) - Line-based chunking

## Contributing

1. Follow the implementation plan in `INDEXER.md`
2. Implement tasks in order for proper dependencies
3. Add comprehensive tests for new functionality
4. Update documentation as needed

## License

MIT License - see LICENSE file for details.
