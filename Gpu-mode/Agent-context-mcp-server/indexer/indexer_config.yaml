# RAG Indexer Configuration
# Customize these settings for your use case

# Input settings
repository_path: /path/to/repo
output_path: ./index_output

# File filtering
include_extensions:
  - .py
  - .js
  - .ts
  - .jsx
  - .tsx
  - .md
  - .json
  - .yaml
  - .yml
  - .toml
  - .txt
  - .rst
  - .go
  - .rs
  - .java
  - .cpp
  - .c
  - .h

ignore_patterns:
  - node_modules/
  - "*.pyc"
  - .git/
  - __pycache__/
  - venv/
  - .env/
  - dist/
  - build/
  - .pytest_cache/
  - .mypy_cache/
  - .coverage
  - "*.log"
  - "*.tmp"
  - .DS_Store
  - Thumbs.db

max_file_size_mb: 10.0

# Chunking settings
max_chunk_size: 1000
overlap_size: 100
preserve_structure: true

# Embedding settings
embedding_model: jina-embeddings-v2-base-code  # Options: all-MiniLM-L6-v2, all-mpnet-base-v2, jinaai/jina-embeddings-v2-base-code
batch_size: 32
device: cpu  # or 'cuda' for GPU acceleration

# Storage settings
db_path: ./chroma_db
collection_name: codebase_chunks

# Performance settings
max_workers: 4
memory_limit_gb: 2.0
