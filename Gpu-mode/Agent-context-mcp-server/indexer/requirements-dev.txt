# Development dependencies for RAG Indexer
# Install with: pip install -r requirements-dev.txt

# Include core dependencies
-r requirements.txt

# Testing framework
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-asyncio>=0.21.0

# Code formatting and linting
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0

# Type checking
mypy>=1.0.0
types-PyYAML>=6.0.0
types-tqdm>=4.65.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0

# Development utilities
pre-commit>=3.0.0
ipython>=8.0.0
jupyter>=1.0.0

# Performance profiling
memory-profiler>=0.60.0
line-profiler>=4.0.0
