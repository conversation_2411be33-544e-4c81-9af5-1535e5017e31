"""
Generic Fallback Parser

This module provides a generic parser for unsupported file types.
It performs simple line-based chunking while trying to preserve
structure and extract basic information.

Key features:
- Line-based chunking with intelligent break points
- Basic comment extraction
- Configuration file handling (JSON, YAML, TOML)
- Binary file detection and rejection
"""

import json
import re
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

from .base import BaseParser, ParseResult

logger = logging.getLogger(__name__)


class GenericParser(BaseParser):
    """Generic parser for unsupported file types."""
    
    def get_language(self) -> str:
        """Get language identifier."""
        return "generic"
    
    def get_supported_extensions(self) -> List[str]:
        """Get supported file extensions (all)."""
        return ['*']  # Supports all extensions as fallback
    
    def can_parse(self, file_path: Path) -> bool:
        """Generic parser can handle any text file."""
        return True  # Always returns True as fallback
    
    def parse(self, content: str, file_path: Path) -> ParseResult:
        """
        Parse generic content and extract chunks.
        
        Args:
            content: File content
            file_path: Path to the file
            
        Returns:
            ParseResult with extracted chunks
        """
        chunks = []
        errors = []
        
        try:
            # Determine file type and parse accordingly
            file_type = self._detect_file_type(file_path, content)
            
            if file_type == 'json':
                chunks = self._parse_json(content, file_path)
            elif file_type == 'yaml':
                chunks = self._parse_yaml(content, file_path)
            elif file_type == 'toml':
                chunks = self._parse_toml(content, file_path)
            elif file_type == 'config':
                chunks = self._parse_config(content, file_path)
            else:
                chunks = self._parse_text(content, file_path)
                
        except Exception as e:
            logger.error(f"Error parsing generic file {file_path}: {e}")
            errors.append(str(e))
            # Fallback to simple text parsing
            chunks = self._parse_text(content, file_path)
        
        metadata = {
            'language': 'generic',
            'file_path': str(file_path),
            'total_chunks': len(chunks),
            'detected_type': self._detect_file_type(file_path, content)
        }
        
        return ParseResult(chunks=chunks, metadata=metadata, errors=errors)
    
    def _detect_file_type(self, file_path: Path, content: str) -> str:
        """Detect the type of file based on extension and content."""
        extension = file_path.suffix.lower()
        
        # Check by extension first
        if extension in ['.json']:
            return 'json'
        elif extension in ['.yaml', '.yml']:
            return 'yaml'
        elif extension in ['.toml']:
            return 'toml'
        elif extension in ['.ini', '.cfg', '.conf', '.config']:
            return 'config'
        elif extension in ['.log']:
            return 'log'
        elif extension in ['.csv']:
            return 'csv'
        elif extension in ['.xml']:
            return 'xml'
        elif extension in ['.sql']:
            return 'sql'
        
        # Try to detect by content
        content_stripped = content.strip()
        
        if content_stripped.startswith('{') and content_stripped.endswith('}'):
            try:
                json.loads(content)
                return 'json'
            except:
                pass
        
        if content_stripped.startswith('[') and content_stripped.endswith(']'):
            try:
                json.loads(content)
                return 'json'
            except:
                pass
        
        # Check for YAML patterns
        if re.match(r'^[\w\-]+:\s*', content_stripped) or '---' in content:
            return 'yaml'
        
        # Check for XML
        if content_stripped.startswith('<?xml') or content_stripped.startswith('<'):
            return 'xml'
        
        # Check for SQL
        if re.search(r'\b(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER)\b', content, re.IGNORECASE):
            return 'sql'
        
        return 'text'
    
    def _parse_json(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Parse JSON files."""
        try:
            # Try to parse as JSON to validate
            json_data = json.loads(content)
            
            # For small JSON files, treat as single chunk
            lines = content.split('\n')
            if len(lines) <= 100:
                return [{
                    'content': content,
                    'metadata': {
                        'chunk_type': 'json_file',
                        'start_line': 1,
                        'end_line': len(lines),
                        'language': 'json',
                        'file_path': str(file_path),
                        'json_valid': True,
                        'json_type': type(json_data).__name__
                    }
                }]
            
            # For large JSON files, split by top-level keys
            return self._split_large_json(content, file_path, json_data)
            
        except json.JSONDecodeError as e:
            logger.warning(f"Invalid JSON in {file_path}: {e}")
            # Treat as regular text
            return self._parse_text(content, file_path)
    
    def _parse_yaml(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Parse YAML files."""
        try:
            # Try to import yaml
            import yaml
            yaml_data = yaml.safe_load(content)
            
            lines = content.split('\n')
            
            # For small YAML files, treat as single chunk
            if len(lines) <= 100:
                return [{
                    'content': content,
                    'metadata': {
                        'chunk_type': 'yaml_file',
                        'start_line': 1,
                        'end_line': len(lines),
                        'language': 'yaml',
                        'file_path': str(file_path),
                        'yaml_valid': True,
                        'yaml_type': type(yaml_data).__name__
                    }
                }]
            
            # For large YAML files, split by top-level sections
            return self._split_yaml_sections(content, file_path)
            
        except ImportError:
            logger.warning("PyYAML not available, treating YAML as text")
            return self._parse_text(content, file_path)
        except Exception as e:
            logger.warning(f"Invalid YAML in {file_path}: {e}")
            return self._parse_text(content, file_path)
    
    def _parse_toml(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Parse TOML files."""
        try:
            # Try to import toml
            import tomllib
            toml_data = tomllib.loads(content)
            
            lines = content.split('\n')
            
            return [{
                'content': content,
                'metadata': {
                    'chunk_type': 'toml_file',
                    'start_line': 1,
                    'end_line': len(lines),
                    'language': 'toml',
                    'file_path': str(file_path),
                    'toml_valid': True,
                    'sections': list(toml_data.keys()) if isinstance(toml_data, dict) else []
                }
            }]
            
        except ImportError:
            logger.warning("tomllib not available, treating TOML as text")
            return self._parse_text(content, file_path)
        except Exception as e:
            logger.warning(f"Invalid TOML in {file_path}: {e}")
            return self._parse_text(content, file_path)
    
    def _parse_config(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Parse configuration files (INI style)."""
        lines = content.split('\n')
        chunks = []
        
        # Find sections in INI-style config
        current_section = []
        section_name = None
        start_line = 1
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            # Check for section header [section]
            if line_stripped.startswith('[') and line_stripped.endswith(']'):
                # Save previous section
                if current_section:
                    section_content = '\n'.join(current_section)
                    chunks.append({
                        'content': section_content,
                        'metadata': {
                            'chunk_type': 'config_section',
                            'section_name': section_name,
                            'start_line': start_line,
                            'end_line': i,
                            'language': 'config',
                            'file_path': str(file_path)
                        }
                    })
                
                # Start new section
                section_name = line_stripped[1:-1]
                current_section = [line]
                start_line = i + 1
            else:
                current_section.append(line)
        
        # Save last section
        if current_section:
            section_content = '\n'.join(current_section)
            chunks.append({
                'content': section_content,
                'metadata': {
                    'chunk_type': 'config_section',
                    'section_name': section_name or 'main',
                    'start_line': start_line,
                    'end_line': len(lines),
                    'language': 'config',
                    'file_path': str(file_path)
                }
            })
        
        # If no sections found, treat as single chunk
        if not chunks:
            chunks = [{
                'content': content,
                'metadata': {
                    'chunk_type': 'config_file',
                    'start_line': 1,
                    'end_line': len(lines),
                    'language': 'config',
                    'file_path': str(file_path)
                }
            }]
        
        return chunks
    
    def _parse_text(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Parse generic text files with line-based chunking."""
        lines = content.split('\n')
        chunks = []
        
        # For small files, create single chunk
        if len(lines) <= 50:
            return [{
                'content': content,
                'metadata': {
                    'chunk_type': 'text_file',
                    'start_line': 1,
                    'end_line': len(lines),
                    'language': 'text',
                    'file_path': str(file_path),
                    'total_lines': len(lines)
                }
            }]
        
        # For larger files, split into chunks
        chunk_size = 30  # lines per chunk
        overlap = 5      # lines overlap
        
        for i in range(0, len(lines), chunk_size - overlap):
            chunk_lines = lines[i:i + chunk_size]
            chunk_content = '\n'.join(chunk_lines)
            
            # Extract any comments from this chunk
            comments = self._extract_comments(chunk_content)
            
            chunk = {
                'content': chunk_content,
                'metadata': {
                    'chunk_type': 'text_chunk',
                    'start_line': i + 1,
                    'end_line': min(i + chunk_size, len(lines)),
                    'language': 'text',
                    'file_path': str(file_path),
                    'chunk_lines': len(chunk_lines),
                    'comments': comments
                }
            }
            chunks.append(chunk)
        
        return chunks
    
    def _split_large_json(self, content: str, file_path: Path, json_data: Any) -> List[Dict[str, Any]]:
        """Split large JSON files by top-level structure."""
        # This is a simplified implementation
        # TODO: Implement proper JSON structure-based splitting
        return self._parse_text(content, file_path)
    
    def _split_yaml_sections(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Split YAML files by top-level sections."""
        lines = content.split('\n')
        chunks = []
        
        current_section = []
        section_key = None
        start_line = 1
        
        for i, line in enumerate(lines):
            # Check for top-level key (no indentation)
            if line and not line.startswith(' ') and ':' in line and not line.startswith('#'):
                # Save previous section
                if current_section:
                    section_content = '\n'.join(current_section)
                    chunks.append({
                        'content': section_content,
                        'metadata': {
                            'chunk_type': 'yaml_section',
                            'section_key': section_key,
                            'start_line': start_line,
                            'end_line': i,
                            'language': 'yaml',
                            'file_path': str(file_path)
                        }
                    })
                
                # Start new section
                section_key = line.split(':')[0].strip()
                current_section = [line]
                start_line = i + 1
            else:
                current_section.append(line)
        
        # Save last section
        if current_section:
            section_content = '\n'.join(current_section)
            chunks.append({
                'content': section_content,
                'metadata': {
                    'chunk_type': 'yaml_section',
                    'section_key': section_key or 'main',
                    'start_line': start_line,
                    'end_line': len(lines),
                    'language': 'yaml',
                    'file_path': str(file_path)
                }
            })
        
        return chunks if chunks else self._parse_text(content, file_path)
    
    def _extract_comments(self, content: str) -> List[str]:
        """Extract comments from content."""
        comments = []
        
        # Common comment patterns
        patterns = [
            r'#.*$',      # Shell/Python style
            r'//.*$',     # C/Java style
            r'/\*.*?\*/', # C block comments
            r'<!--.*?-->', # HTML comments
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
            comments.extend(matches)
        
        return [comment.strip() for comment in comments if comment.strip()]
