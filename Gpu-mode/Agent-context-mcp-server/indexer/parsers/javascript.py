"""
JavaScript/TypeScript Parser Implementation

This module provides JavaScript and TypeScript parsing using tree-sitter
to extract semantic information from JS/TS source code files.

Key features:
- Function and arrow function extraction
- Class and method extraction
- Import/export statement parsing
- JSDoc comment extraction
- TypeScript interface and type extraction
"""

import re
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

from .base import BaseParser, ParseResult

logger = logging.getLogger(__name__)


class JavaScriptParser(BaseParser):
    """Parser for JavaScript and TypeScript source code."""
    
    def __init__(self, max_chunk_size: int = 1000, overlap_size: int = 100):
        """Initialize JavaScript/TypeScript parser."""
        super().__init__(max_chunk_size, overlap_size)
        self._parser = None
        self._language = None
    
    def get_language(self) -> str:
        """Get language identifier."""
        return "javascript"
    
    def get_supported_extensions(self) -> List[str]:
        """Get supported file extensions."""
        return ['.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs']
    
    def _init_tree_sitter(self):
        """Initialize tree-sitter parser (lazy loading)."""
        if self._parser is None:
            try:
                import tree_sitter_javascript as tsjs
                from tree_sitter import Language, Parser
                
                self._language = Language(tsjs.language())
                self._parser = Parser(self._language)
                logger.debug("Initialized tree-sitter JavaScript parser")
                
            except ImportError:
                logger.warning("tree-sitter-javascript not available, falling back to regex parsing")
                self._parser = "regex"  # Flag for regex fallback
    
    def parse(self, content: str, file_path: Path) -> ParseResult:
        """
        Parse JavaScript/TypeScript content and extract semantic chunks.
        
        Args:
            content: JavaScript/TypeScript source code
            file_path: Path to the JS/TS file
            
        Returns:
            ParseResult with extracted chunks
        """
        self._init_tree_sitter()
        
        chunks = []
        errors = []
        
        try:
            if self._parser == "regex":
                # Fallback to regex-based parsing
                chunks = self._parse_with_regex(content, file_path)
            else:
                # Use tree-sitter parsing
                chunks = self._parse_with_tree_sitter(content, file_path)
                
        except Exception as e:
            logger.error(f"Error parsing JavaScript file {file_path}: {e}")
            errors.append(str(e))
            # Fallback to simple chunking
            chunks = self._fallback_parse(content, file_path)
        
        is_typescript = file_path.suffix.lower() in ['.ts', '.tsx']
        
        metadata = {
            'language': 'typescript' if is_typescript else 'javascript',
            'file_path': str(file_path),
            'total_chunks': len(chunks),
            'parser_type': 'tree-sitter' if self._parser != "regex" else 'regex',
            'is_typescript': is_typescript
        }
        
        return ParseResult(chunks=chunks, metadata=metadata, errors=errors)
    
    def _parse_with_tree_sitter(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Parse using tree-sitter."""
        tree = self._parser.parse(bytes(content, "utf8"))
        chunks = []
        
        # Extract imports/exports
        imports = self._extract_imports_tree_sitter(tree, content)
        
        # Extract functions
        chunks.extend(self._extract_functions_tree_sitter(tree, content, file_path, imports))
        
        # Extract classes
        chunks.extend(self._extract_classes_tree_sitter(tree, content, file_path, imports))
        
        # Extract interfaces (TypeScript)
        if file_path.suffix.lower() in ['.ts', '.tsx']:
            chunks.extend(self._extract_interfaces_tree_sitter(tree, content, file_path, imports))
        
        return chunks
    
    def _parse_with_regex(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Fallback regex-based parsing."""
        chunks = []
        lines = content.split('\n')
        
        # Extract imports
        imports = self.extract_imports(content)
        
        # Find functions (regular and arrow functions)
        func_patterns = [
            r'^(\s*)function\s+(\w+)\s*\([^)]*\)\s*{',  # function declarations
            r'^(\s*)const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*{',  # arrow functions
            r'^(\s*)(\w+)\s*:\s*\([^)]*\)\s*=>\s*{',  # object method arrow functions
        ]
        
        for pattern in func_patterns:
            for i, line in enumerate(lines):
                match = re.match(pattern, line)
                if match:
                    indent, func_name = match.groups()
                    
                    # Find function end (simple brace matching)
                    func_lines = [line]
                    brace_count = line.count('{') - line.count('}')
                    j = i + 1
                    
                    while j < len(lines) and brace_count > 0:
                        current_line = lines[j]
                        func_lines.append(current_line)
                        brace_count += current_line.count('{') - current_line.count('}')
                        j += 1
                    
                    func_content = '\n'.join(func_lines)
                    jsdoc = self._extract_jsdoc_regex(content, i)
                    
                    chunk = {
                        'content': func_content,
                        'metadata': {
                            'chunk_type': 'function',
                            'function_name': func_name,
                            'start_line': i + 1,
                            'end_line': i + len(func_lines),
                            'jsdoc': jsdoc,
                            'imports': imports,
                            'language': 'typescript' if file_path.suffix.lower() in ['.ts', '.tsx'] else 'javascript',
                            'file_path': str(file_path)
                        }
                    }
                    chunks.append(chunk)
        
        return chunks
    
    def _extract_functions_tree_sitter(self, tree, content: str, file_path: Path, imports: List[str]) -> List[Dict[str, Any]]:
        """Extract functions using tree-sitter."""
        chunks = []
        
        def traverse(node, depth=0):
            if node.type in ["function_declaration", "arrow_function", "function_expression", "method_definition"]:
                start_line = node.start_point[0] + 1
                end_line = node.end_point[0] + 1
                func_content = content[node.start_byte:node.end_byte]
                
                # Extract function name
                func_name = self._extract_function_name_tree_sitter(node, content)
                
                # Extract JSDoc
                jsdoc = self._extract_jsdoc_tree_sitter(node, content)
                
                # Check if it's async
                is_async = self._is_async_function_tree_sitter(node, content)
                
                chunk = {
                    'content': func_content,
                    'metadata': {
                        'chunk_type': 'function',
                        'function_name': func_name,
                        'start_line': start_line,
                        'end_line': end_line,
                        'jsdoc': jsdoc,
                        'is_async': is_async,
                        'function_type': node.type,
                        'imports': imports,
                        'language': 'typescript' if file_path.suffix.lower() in ['.ts', '.tsx'] else 'javascript',
                        'file_path': str(file_path),
                        'complexity_score': self.calculate_complexity(func_content)
                    }
                }
                chunks.append(chunk)
            
            for child in node.children:
                traverse(child, depth + 1)
        
        traverse(tree.root_node)
        return chunks
    
    def _extract_classes_tree_sitter(self, tree, content: str, file_path: Path, imports: List[str]) -> List[Dict[str, Any]]:
        """Extract classes using tree-sitter."""
        chunks = []
        
        def traverse(node, depth=0):
            if node.type == "class_declaration":
                start_line = node.start_point[0] + 1
                end_line = node.end_point[0] + 1
                class_content = content[node.start_byte:node.end_byte]
                
                # Extract class name
                class_name = self._extract_class_name_tree_sitter(node, content)
                
                # Extract JSDoc
                jsdoc = self._extract_jsdoc_tree_sitter(node, content)
                
                # Extract extends clause
                extends = self._extract_extends_tree_sitter(node, content)
                
                chunk = {
                    'content': class_content,
                    'metadata': {
                        'chunk_type': 'class',
                        'class_name': class_name,
                        'start_line': start_line,
                        'end_line': end_line,
                        'jsdoc': jsdoc,
                        'extends': extends,
                        'imports': imports,
                        'language': 'typescript' if file_path.suffix.lower() in ['.ts', '.tsx'] else 'javascript',
                        'file_path': str(file_path),
                        'complexity_score': self.calculate_complexity(class_content)
                    }
                }
                chunks.append(chunk)
            
            for child in node.children:
                traverse(child, depth + 1)
        
        traverse(tree.root_node)
        return chunks
    
    def _extract_interfaces_tree_sitter(self, tree, content: str, file_path: Path, imports: List[str]) -> List[Dict[str, Any]]:
        """Extract TypeScript interfaces using tree-sitter."""
        chunks = []
        
        def traverse(node, depth=0):
            if node.type == "interface_declaration":
                start_line = node.start_point[0] + 1
                end_line = node.end_point[0] + 1
                interface_content = content[node.start_byte:node.end_byte]
                
                # Extract interface name
                interface_name = self._extract_interface_name_tree_sitter(node, content)
                
                # Extract JSDoc
                jsdoc = self._extract_jsdoc_tree_sitter(node, content)
                
                chunk = {
                    'content': interface_content,
                    'metadata': {
                        'chunk_type': 'interface',
                        'interface_name': interface_name,
                        'start_line': start_line,
                        'end_line': end_line,
                        'jsdoc': jsdoc,
                        'imports': imports,
                        'language': 'typescript',
                        'file_path': str(file_path)
                    }
                }
                chunks.append(chunk)
            
            for child in node.children:
                traverse(child, depth + 1)
        
        traverse(tree.root_node)
        return chunks
    
    def _extract_imports_tree_sitter(self, tree, content: str) -> List[str]:
        """Extract import/export statements using tree-sitter."""
        imports = []
        
        def traverse(node):
            if node.type in ["import_statement", "export_statement"]:
                import_text = content[node.start_byte:node.end_byte]
                imports.append(import_text.strip())
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return imports
    
    def _extract_function_name_tree_sitter(self, node, content: str) -> str:
        """Extract function name from tree-sitter node."""
        # Try to find identifier node
        for child in node.children:
            if child.type == "identifier":
                return content[child.start_byte:child.end_byte]
        
        return "anonymous"
    
    def _extract_class_name_tree_sitter(self, node, content: str) -> str:
        """Extract class name from tree-sitter node."""
        for child in node.children:
            if child.type == "identifier":
                return content[child.start_byte:child.end_byte]
        
        return "unknown"
    
    def _extract_interface_name_tree_sitter(self, node, content: str) -> str:
        """Extract interface name from tree-sitter node."""
        for child in node.children:
            if child.type == "type_identifier":
                return content[child.start_byte:child.end_byte]
        
        return "unknown"
    
    def _extract_jsdoc_tree_sitter(self, node, content: str) -> Optional[str]:
        """Extract JSDoc comment from tree-sitter node."""
        # Look for comment node before the function/class
        # This is a simplified implementation
        return None
    
    def _extract_extends_tree_sitter(self, node, content: str) -> Optional[str]:
        """Extract extends clause from class node."""
        for child in node.children:
            if child.type == "class_heritage":
                return content[child.start_byte:child.end_byte]
        
        return None
    
    def _is_async_function_tree_sitter(self, node, content: str) -> bool:
        """Check if function is async."""
        func_text = content[node.start_byte:node.end_byte]
        return 'async' in func_text[:50]  # Check first 50 chars
    
    def _extract_jsdoc_regex(self, content: str, line_num: int) -> Optional[str]:
        """Extract JSDoc comment using regex."""
        lines = content.split('\n')
        
        # Look backwards for JSDoc comment
        for i in range(line_num - 1, max(0, line_num - 10), -1):
            line = lines[i].strip()
            if line.startswith('/**'):
                # Found start of JSDoc, collect until */
                jsdoc_lines = []
                for j in range(i, line_num):
                    jsdoc_lines.append(lines[j])
                    if '*/' in lines[j]:
                        break
                
                jsdoc = '\n'.join(jsdoc_lines)
                return self._clean_jsdoc(jsdoc)
        
        return None
    
    def _clean_jsdoc(self, jsdoc: str) -> str:
        """Clean and format JSDoc comment."""
        # Remove /** and */ and leading *
        lines = jsdoc.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('/**'):
                line = line[3:]
            elif line.startswith('*/'):
                continue
            elif line.startswith('*'):
                line = line[1:]
            
            cleaned_lines.append(line.strip())
        
        return ' '.join(cleaned_lines).strip()
    
    def _fallback_parse(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Simple fallback parsing when tree-sitter fails."""
        lines = content.split('\n')
        
        chunk = {
            'content': content,
            'metadata': {
                'chunk_type': 'file',
                'start_line': 1,
                'end_line': len(lines),
                'language': 'typescript' if file_path.suffix.lower() in ['.ts', '.tsx'] else 'javascript',
                'file_path': str(file_path),
                'total_lines': len(lines),
                'parsing_method': 'fallback'
            }
        }
        
        return [chunk]
    
    def extract_imports(self, content: str) -> List[str]:
        """Extract import/export statements using regex."""
        imports = []
        
        # Match import/export statements
        patterns = [
            r'^import\s+.*?from\s+[\'"][^\'"]+[\'"]',
            r'^import\s+[\'"][^\'"]+[\'"]',
            r'^export\s+.*?from\s+[\'"][^\'"]+[\'"]',
            r'^export\s+\{.*?\}',
            r'^export\s+default\s+.*?',
        ]
        
        for line in content.split('\n'):
            line = line.strip()
            for pattern in patterns:
                if re.match(pattern, line):
                    imports.append(line)
                    break
        
        return imports
