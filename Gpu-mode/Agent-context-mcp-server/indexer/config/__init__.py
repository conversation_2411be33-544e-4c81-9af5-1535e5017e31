"""
Configuration Management

This package handles configuration loading, validation, and management
for the RAG indexer system.

Key components:
- settings: Configuration data models and loading functions
- Default configuration templates
- Environment variable support
- Configuration validation
"""

from .settings import IndexConfig, load_config, create_default_config

__all__ = ["IndexConfig", "load_config", "create_default_config"]
