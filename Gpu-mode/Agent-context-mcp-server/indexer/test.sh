#!/bin/bash

# RAG Indexer Test Runner Script
# This script runs the complete test suite for the RAG Indexer

set -e  # Exit on any error

echo "🧪 Running RAG Indexer test suite..."

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  Virtual environment not detected. Attempting to activate..."
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
        echo "✅ Virtual environment activated"
    else
        echo "❌ Virtual environment not found. Please run ./install.sh first."
        exit 1
    fi
fi

# Check if pytest is available
if ! command -v pytest &> /dev/null; then
    echo "❌ pytest not found. Please run ./install.sh to install dependencies."
    exit 1
fi

echo "🔍 Running code quality checks..."

# Run black formatting check
echo "📝 Checking code formatting with black..."
if black --check --diff .; then
    echo "✅ Code formatting check passed"
else
    echo "❌ Code formatting issues found. Run 'black .' to fix them."
    exit 1
fi

# Run flake8 linting
echo "🔍 Running flake8 linting..."
if flake8 .; then
    echo "✅ Linting check passed"
else
    echo "❌ Linting issues found. Please fix them before proceeding."
    exit 1
fi

# Run mypy type checking
echo "🔍 Running mypy type checking..."
if mypy indexer/; then
    echo "✅ Type checking passed"
else
    echo "❌ Type checking issues found. Please fix them before proceeding."
    exit 1
fi

echo "🧪 Running unit tests..."

# Create tests directory if it doesn't exist
if [ ! -d "tests" ]; then
    echo "📁 Creating tests directory..."
    mkdir -p tests
    touch tests/__init__.py
fi

# Run pytest with coverage
echo "🏃 Running pytest with coverage..."
if pytest tests/ --cov=indexer --cov-report=term-missing --cov-report=html --cov-fail-under=80 -v; then
    echo "✅ All tests passed!"
else
    echo "❌ Some tests failed. Please fix them before proceeding."
    exit 1
fi

echo ""
echo "🎉 All tests and quality checks passed!"
echo ""
echo "📊 Coverage report generated in htmlcov/index.html"
echo "🔍 To view coverage report: open htmlcov/index.html"
echo ""
