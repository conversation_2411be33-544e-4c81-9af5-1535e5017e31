"""
Utility Functions

This package contains utility functions and helpers for the RAG indexer:
- gitignore: .gitignore parsing and pattern matching
- logging: Logging setup and configuration
- Common helper functions
"""

from .logging import setup_logging, get_logger
from .gitignore import GitignoreParser, parse_gitignore

__all__ = ["setup_logging", "get_logger", "GitignoreParser", "parse_gitignore"]
