"""
RAG Indexer Package

The RAG Indexer is a component of theAgent Context MCP Server system. 
It's an offline process that takes a codebase and prepares it for semantic 
search by creating vector embeddings and storing them in a searchable database.

Main components:
- core: Core functionality for traversal, chunking, embedding, and storage
- parsers: Language-specific parsers for semantic code analysis
- config: Configuration management and settings
- utils: Utility functions for gitignore parsing, logging, etc.
"""

__version__ = "0.1.0"
__author__ = "ben.horowitz"

from .core.chunking import CodeChunk
from .config.settings import IndexConfig

__all__ = ["CodeChunk", "IndexConfig"]
