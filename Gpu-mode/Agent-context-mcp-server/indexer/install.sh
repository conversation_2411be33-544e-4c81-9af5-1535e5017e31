#!/bin/bash

# RAG Indexer Development Environment Setup Script
# This script sets up the development environment for the RAG Indexer

set -e  # Exit on any error

echo "🚀 Setting up RAG Indexer development environment..."

# Check if Python 3.8+ is available
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" 2>/dev/null; then
    echo "❌ Error: Python 3.8 or higher is required. Found: $python_version"
    echo "Please install Python 3.8+ and try again."
    exit 1
fi

echo "✅ Python version check passed: $(python3 --version)"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install core dependencies
echo "📚 Installing core dependencies..."
pip install -r requirements.txt

# Install development dependencies
echo "🛠️  Installing development dependencies..."
pip install pytest>=7.0.0 pytest-cov>=4.0.0 black>=23.0.0 flake8>=6.0.0 mypy>=1.0.0

# Install package in development mode
echo "🔗 Installing package in development mode..."
pip install -e .

# Verify installation
echo "🧪 Verifying installation..."
# Test from parent directory to avoid naming conflicts
cd ..
if python -c "from indexer.core.traversal import FileType; from indexer.config.settings import IndexConfig; print('✅ Package import successful')" 2>/dev/null; then
    echo "✅ Installation verification passed"
    cd indexer
else
    echo "❌ Installation verification failed"
    cd indexer
    exit 1
fi

# Check if tree-sitter languages are available
echo "🌳 Checking tree-sitter language support..."
if python -c "import tree_sitter_python; import tree_sitter_javascript; print('✅ Tree-sitter languages available')" 2>/dev/null; then
    echo "✅ Tree-sitter language support verified"
else
    echo "⚠️  Warning: Some tree-sitter languages may not be available"
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "To activate the environment in the future, run:"
echo "  source venv/bin/activate"
echo ""
echo "To run the indexer (from parent directory):"
echo "  cd .. && python -m indexer --help"
echo ""
echo "To run tests:"
echo "  ./test.sh"
echo ""
echo "To format code:"
echo "  black ."
echo ""
echo "To run linting:"
echo "  flake8 ."
echo ""
