Metadata-Version: 2.4
Name: rag-indexer
Version: 0.1.0
Summary: RAG Indexer Component of the Agent Context MCP Server
Author: ben.horowitz
License: MIT
Project-URL: Homepage, https://github.com/b9r5/agent-context-mcp-server
Project-URL: Repository, https://github.com/b9r5/agent-context-mcp-server
Project-URL: Issues, https://github.com/b9r5/agent-context-mcp-server/issues
Keywords: rag,indexer,semantic-search,code-analysis,embeddings
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Text Processing :: Indexing
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: tree-sitter>=0.20.4
Requires-Dist: tree-sitter-python>=0.20.4
Requires-Dist: tree-sitter-javascript>=0.20.3
Requires-Dist: pydantic>=2.4.2
Requires-Dist: pyyaml>=6.0.1
Requires-Dist: click>=8.1.7
Requires-Dist: tqdm>=4.66.1
Requires-Dist: gitpython>=3.1.40
Provides-Extra: embedding
Requires-Dist: sentence-transformers>=2.2.2; extra == "embedding"
Requires-Dist: chromadb>=0.4.15; extra == "embedding"
Requires-Dist: openai>=1.0.0; extra == "embedding"
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Provides-Extra: all
Requires-Dist: rag-indexer[dev,embedding]; extra == "all"

# RAG Indexer Design & Implementation Guide

## Overview

The RAG Indexer is Component 1 of the Agent Context MCP Server system. It's an offline process that takes a codebase and prepares it for semantic search by creating vector embeddings and storing them in a searchable database.

## Architecture

```
Input: Codebase Directory
    ↓
Code Traversal & File Discovery
    ↓
Semantic Code Chunking (using tree-sitter)
    ↓
Vector Embedding Generation
    ↓
Vector Database Storage (ChromaDB)
    ↓
Output: Searchable Index
```

## Detailed Design

### 1. Input Processing

**Input**: Path to a local code repository
**Supported File Types**: 
- Source code: `.py`, `.js`, `.ts`, `.jsx`, `.tsx`, `.go`, `.rs`, `.java`, `.cpp`, `.c`, `.h`
- Documentation: `.md`, `.rst`, `.txt`
- Configuration: `.json`, `.yaml`, `.yml`, `.toml`

### 2. Code Traversal

**Objective**: Recursively scan the repository for relevant files while respecting common ignore patterns.

**Implementation Strategy**:
- Use `pathlib` for cross-platform path handling
- Implement `.gitignore` parsing to respect project ignore rules
- Add default ignore patterns for common directories: `node_modules/`, `.git/`, `__pycache__/`, `venv/`, `.env/`
- Support custom ignore patterns via configuration

**Key Functions**:
```python
def discover_files(repo_path: Path, ignore_patterns: List[str]) -> List[Path]
def should_ignore_file(file_path: Path, ignore_patterns: List[str]) -> bool
def get_file_type(file_path: Path) -> FileType
```

### 3. Semantic Code Chunking

**Objective**: Parse code into semantically meaningful chunks rather than arbitrary line-based splits.

**Strategy by File Type**:

#### Source Code Files
Use `tree-sitter` parsers to extract:
- **Functions/Methods**: Complete function definitions with docstrings
- **Classes**: Class definitions with their methods
- **Top-level statements**: Imports, constants, module-level code
- **Comments**: Significant comment blocks

**Chunking Rules**:
- Each function/method = 1 chunk
- Large classes split into: class definition + individual methods
- Module-level code grouped into logical blocks
- Maximum chunk size: 1000 tokens (configurable)

#### Documentation Files
- Split by headers (H1, H2, H3 in Markdown)
- Preserve section context
- Include code blocks with surrounding text

#### Configuration Files
- Treat entire file as one chunk for small files (<500 lines)
- For large configs, split by top-level sections

**Key Functions**:
```python
def chunk_source_code(content: str, language: str) -> List[CodeChunk]
def chunk_documentation(content: str, file_type: str) -> List[DocChunk]
def extract_functions(tree: Tree, source: bytes) -> List[FunctionNode]
def extract_classes(tree: Tree, source: bytes) -> List[ClassNode]
```

### 4. Embedding Generation

**Model Selection**: Use sentence-transformers for local processing
- Primary: `jinaai/jina-embeddings-v2-base-code` (explicitly designed for code search and retrieval)
- Strong alternative: `BAAI/bge-large-en-v1.5` (a general-purpose text embedding model that is a top performer on retrieval benchmarks)
- Other alternatives: `all-MiniLM-L6-v2` (fast, good quality), `all-mpnet-base-v2` (higher quality, slower)
- Fallback: OpenAI `text-embedding-ada-002` (requires API key)

**Embedding Strategy**:
- Generate embeddings for chunk content + metadata
- Include file path and chunk type in embedding context
- Batch processing for efficiency
- Handle rate limiting for API-based embeddings

**Metadata Enrichment**:
Each chunk includes:
```python
@dataclass
class ChunkMetadata:
    file_path: str
    start_line: int
    end_line: int
    chunk_type: str  # 'function', 'class', 'documentation', etc.
    language: str
    function_name: Optional[str]
    class_name: Optional[str]
    docstring: Optional[str]
    imports: List[str]
```

### 5. Vector Storage

**Database**: ChromaDB (local, persistent)
- Easy setup and deployment
- Good performance for medium-scale codebases
- Python-native with good documentation

**Schema Design**:
```python
collection_schema = {
    "name": "codebase_chunks",
    "metadata": {
        "description": "Semantic chunks from codebase",
        "embedding_model": "all-MiniLM-L6-v2",
        "chunk_strategy": "tree-sitter-semantic"
    }
}

# Each document stored with:
{
    "id": "unique_chunk_id",
    "embedding": [0.1, 0.2, ...],  # 384-dim vector
    "document": "chunk_content",
    "metadata": {
        "file_path": "src/main.py",
        "start_line": 45,
        "end_line": 67,
        "chunk_type": "function",
        "function_name": "authenticate_user",
        "language": "python"
    }
}
```

## Implementation Structure

```
indexer/
├── __init__.py
├── main.py              # CLI entry point
├── core/
│   ├── __init__.py
│   ├── traversal.py     # File discovery and filtering
│   ├── chunking.py      # Code parsing and chunking
│   ├── embedding.py     # Vector generation
│   └── storage.py       # Database operations
├── parsers/
│   ├── __init__.py
│   ├── base.py          # Abstract parser interface
│   ├── python.py        # Python-specific parsing
│   ├── javascript.py    # JS/TS parsing
│   ├── markdown.py      # Documentation parsing
│   └── generic.py       # Fallback parser
├── config/
│   ├── __init__.py
│   └── settings.py      # Configuration management
└── utils/
    ├── __init__.py
    ├── gitignore.py     # .gitignore parsing
    └── logging.py       # Logging setup
```

## Configuration

**Config File**: `indexer_config.yaml`
```yaml
# Input settings
repository_path: "/path/to/repo"
output_path: "./index_db"

# File filtering
include_extensions: [".py", ".js", ".ts", ".md"]
ignore_patterns: ["node_modules/", "*.pyc", ".git/"]
max_file_size_mb: 10

# Chunking settings
max_chunk_size: 1000
overlap_size: 100
preserve_structure: true

# Embedding settings
model_name: "all-MiniLM-L6-v2"
batch_size: 32
device: "cpu"  # or "cuda"

# Storage settings
db_path: "./chroma_db"
collection_name: "codebase_chunks"
```

## CLI Interface

```bash
# Basic indexing
python -m indexer index /path/to/repo

# With custom config
python -m indexer index /path/to/repo --config custom_config.yaml

# Update existing index
python -m indexer update /path/to/repo --incremental

# Query index for testing
python -m indexer query "how to authenticate users"

# Show index statistics
python -m indexer stats
```

## Error Handling & Logging

**Logging Strategy**:
- Structured logging with different levels
- Progress tracking for long operations
- Detailed error reporting with context
- Performance metrics (files/sec, chunks/sec)

**Error Recovery**:
- Skip corrupted files with warnings
- Retry failed embeddings with exponential backoff
- Graceful handling of unsupported file types
- Checkpoint progress for large repositories

## Performance Considerations

**Optimization Targets**:
- Process 10,000 files in <30 minutes
- Memory usage <2GB for typical repositories
- Incremental updates in <5 minutes

**Strategies**:
- Parallel processing for independent operations
- Streaming for large files
- Caching for repeated operations
- Efficient tree-sitter parser reuse

## Testing Strategy

**Unit Tests**:
- Parser accuracy for different languages
- Chunking quality and completeness
- Embedding consistency
- Database operations

**Integration Tests**:
- End-to-end indexing of sample repositories
- Performance benchmarks
- Error handling scenarios

**Test Data**:
- Small synthetic codebases for each language
- Real open-source projects for integration testing
- Edge cases: empty files, binary files, very large files

## Implementation Examples

### Core Data Structures

```python
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from pathlib import Path

@dataclass
class CodeChunk:
    content: str
    file_path: str
    start_line: int
    end_line: int
    chunk_type: str
    language: str
    metadata: Dict[str, Any]

    def to_embedding_text(self) -> str:
        """Convert chunk to text suitable for embedding."""
        context = f"File: {self.file_path}\n"
        if self.metadata.get('function_name'):
            context += f"Function: {self.metadata['function_name']}\n"
        if self.metadata.get('class_name'):
            context += f"Class: {self.metadata['class_name']}\n"
        return context + self.content

@dataclass
class IndexConfig:
    repository_path: Path
    output_path: Path
    include_extensions: List[str]
    ignore_patterns: List[str]
    max_chunk_size: int
    embedding_model: str
    batch_size: int
```

### Tree-sitter Integration Example

```python
import tree_sitter_python as tspython
from tree_sitter import Language, Parser

class PythonChunker:
    def __init__(self):
        self.language = Language(tspython.language())
        self.parser = Parser(self.language)

    def extract_functions(self, source_code: str) -> List[CodeChunk]:
        tree = self.parser.parse(bytes(source_code, "utf8"))
        chunks = []

        def traverse(node, depth=0):
            if node.type == "function_def":
                start_line = node.start_point[0] + 1
                end_line = node.end_point[0] + 1
                func_text = source_code[node.start_byte:node.end_byte]

                # Extract function name
                name_node = node.child_by_field_name("name")
                func_name = source_code[name_node.start_byte:name_node.end_byte]

                # Extract docstring if present
                docstring = self._extract_docstring(node, source_code)

                chunk = CodeChunk(
                    content=func_text,
                    file_path="",  # Set by caller
                    start_line=start_line,
                    end_line=end_line,
                    chunk_type="function",
                    language="python",
                    metadata={
                        "function_name": func_name,
                        "docstring": docstring,
                        "has_decorators": self._has_decorators(node)
                    }
                )
                chunks.append(chunk)

            for child in node.children:
                traverse(child, depth + 1)

        traverse(tree.root_node)
        return chunks
```

### Embedding Pipeline

```python
from sentence_transformers import SentenceTransformer
import chromadb
from typing import List, Tuple

class EmbeddingPipeline:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model = SentenceTransformer(model_name)
        self.client = chromadb.PersistentClient(path="./chroma_db")

    def process_chunks(self, chunks: List[CodeChunk]) -> None:
        """Process chunks in batches and store in vector DB."""
        collection = self.client.get_or_create_collection(
            name="codebase_chunks",
            metadata={"hnsw:space": "cosine"}
        )

        batch_size = 32
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]

            # Prepare texts for embedding
            texts = [chunk.to_embedding_text() for chunk in batch]

            # Generate embeddings
            embeddings = self.model.encode(texts, show_progress_bar=True)

            # Prepare for storage
            ids = [f"{chunk.file_path}:{chunk.start_line}" for chunk in batch]
            documents = [chunk.content for chunk in batch]
            metadatas = [self._prepare_metadata(chunk) for chunk in batch]

            # Store in ChromaDB
            collection.add(
                embeddings=embeddings.tolist(),
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )

    def _prepare_metadata(self, chunk: CodeChunk) -> Dict[str, Any]:
        """Convert chunk metadata for ChromaDB storage."""
        return {
            "file_path": chunk.file_path,
            "start_line": chunk.start_line,
            "end_line": chunk.end_line,
            "chunk_type": chunk.chunk_type,
            "language": chunk.language,
            **chunk.metadata
        }
```

### Main Indexer Class

```python
class CodebaseIndexer:
    def __init__(self, config: IndexConfig):
        self.config = config
        self.embedding_pipeline = EmbeddingPipeline(config.embedding_model)
        self.chunkers = self._initialize_chunkers()

    def index_repository(self) -> None:
        """Main entry point for indexing a repository."""
        print(f"Indexing repository: {self.config.repository_path}")

        # Discover files
        files = self._discover_files()
        print(f"Found {len(files)} files to process")

        # Process files in chunks
        all_chunks = []
        for file_path in files:
            try:
                chunks = self._process_file(file_path)
                all_chunks.extend(chunks)
                print(f"Processed {file_path}: {len(chunks)} chunks")
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                continue

        print(f"Generated {len(all_chunks)} total chunks")

        # Generate embeddings and store
        self.embedding_pipeline.process_chunks(all_chunks)
        print("Indexing complete!")

    def _discover_files(self) -> List[Path]:
        """Discover all relevant files in the repository."""
        files = []
        for file_path in self.config.repository_path.rglob("*"):
            if (file_path.is_file() and
                self._should_include_file(file_path)):
                files.append(file_path)
        return files

    def _should_include_file(self, file_path: Path) -> bool:
        """Check if file should be included in indexing."""
        # Check extension
        if file_path.suffix not in self.config.include_extensions:
            return False

        # Check ignore patterns
        for pattern in self.config.ignore_patterns:
            if pattern in str(file_path):
                return False

        return True
```

## Dependencies

```python
# requirements.txt
tree-sitter==0.20.4
tree-sitter-python==0.20.4
tree-sitter-javascript==0.20.3
sentence-transformers==2.2.2
chromadb==0.4.15
pydantic==2.4.2
pyyaml==6.0.1
click==8.1.7
tqdm==4.66.1
gitpython==3.1.40
```

## Next Steps

### Phase 1: Core Traversal and Chunking Implementation Plan

#### 1.1 Project Setup and Foundation (Tasks 1-3)

**Task 1: Initialize Project Structure**
- Create the complete directory structure as defined in the Implementation Structure section
- Set up `__init__.py` files for all packages
- Create placeholder files for all modules with basic docstrings
- Initialize git repository if not already done
- Set up basic Python packaging structure (pyproject.toml or setup.py)

**Task 2: Dependencies and Environment Setup**
- Create `requirements.txt` with core dependencies for Phase 1:
  - `tree-sitter>=0.20.4`
  - `tree-sitter-python>=0.20.4`
  - `tree-sitter-javascript>=0.20.3`
  - `pydantic>=2.4.2`
  - `pyyaml>=6.0.1`
  - `click>=8.1.7`
  - `tqdm>=4.66.1`
  - `gitpython>=3.1.40` (for .gitignore parsing)
- Set up virtual environment and install dependencies
- Create basic development scripts (install.sh, test.sh)

**Task 3: Core Data Models**
- Implement `CodeChunk` dataclass in `core/chunking.py`
- Implement `IndexConfig` dataclass in `config/settings.py`
- Create `FileType` enum for supported file types
- Add validation and serialization methods using Pydantic
- Create base exception classes for the indexer

#### 1.2 Configuration Management (Tasks 4-5)

**Task 4: Configuration System**
- Implement `config/settings.py` with YAML configuration loading
- Create default configuration template (`indexer_config.yaml`)
- Add configuration validation and error handling
- Support for environment variable overrides
- Configuration merging (default + user config + CLI args)

**Task 5: Logging Infrastructure**
- Implement `utils/logging.py` with structured logging
- Set up different log levels and formatters
- Add progress tracking utilities using tqdm
- Create performance metrics collection framework
- File and console logging configuration

#### 1.3 File Discovery and Traversal (Tasks 6-8)

**Task 6: .gitignore Parser**
- Implement `utils/gitignore.py` for parsing .gitignore files
- Support for glob patterns, negation patterns, and directory patterns
- Handle multiple .gitignore files in nested directories
- Add default ignore patterns for common build artifacts
- Unit tests for various .gitignore pattern scenarios

**Task 7: File Discovery Engine**
- Implement `core/traversal.py` with the main file discovery logic
- `discover_files()` function with recursive directory scanning
- `should_ignore_file()` function integrating .gitignore rules
- `get_file_type()` function for file type detection
- Support for custom ignore patterns and file size limits
- Progress reporting for large repositories

**Task 8: File Type Detection and Filtering**
- Extend file type detection with MIME type checking
- Add support for files without extensions
- Implement file size and encoding validation
- Create file metadata extraction (creation time, size, etc.)
- Add binary file detection and exclusion

#### 1.4 Tree-sitter Integration (Tasks 9-11)

**Task 9: Parser Infrastructure**
- Implement `parsers/base.py` with abstract parser interface
- Create `ParserRegistry` for managing language-specific parsers
- Add parser initialization and caching mechanisms
- Error handling for parser failures and unsupported languages
- Basic parser performance monitoring

**Task 10: Python Parser Implementation**
- Implement `parsers/python.py` with comprehensive Python parsing
- Extract functions, classes, methods, and module-level code
- Handle decorators, async functions, and nested classes
- Extract docstrings and type hints
- Support for Python 3.8+ syntax features
- Comprehensive unit tests with real Python code samples

**Task 11: JavaScript/TypeScript Parser**
- Implement `parsers/javascript.py` for JS/TS parsing
- Extract functions, classes, methods, and arrow functions
- Handle ES6+ features, imports/exports, and JSX
- Support for TypeScript-specific constructs (interfaces, types)
- Extract JSDoc comments and type annotations
- Unit tests covering modern JavaScript patterns

#### 1.5 Code Chunking Engine (Tasks 12-14)

**Task 12: Core Chunking Logic**
- Implement `core/chunking.py` with the main chunking orchestrator
- `chunk_source_code()` function dispatching to language parsers
- Chunk size management and splitting for large functions
- Metadata extraction and enrichment for each chunk
- Context preservation (imports, surrounding code)
- Overlap handling between adjacent chunks

**Task 13: Documentation Parser**
- Implement `parsers/markdown.py` for Markdown documentation
- Split by headers while preserving hierarchy
- Extract code blocks with language detection
- Handle tables, lists, and other Markdown structures
- Support for other documentation formats (RST, plain text)
- Preserve cross-references and links

**Task 14: Generic Fallback Parser**
- Implement `parsers/generic.py` for unsupported file types
- Line-based chunking with intelligent break points
- Preserve indentation and structure where possible
- Handle configuration files (JSON, YAML, TOML)
- Basic comment extraction for unknown languages

#### 1.6 CLI Interface and Integration (Tasks 15-17)

**Task 15: Command Line Interface**
- Implement `main.py` with Click-based CLI
- Support for basic indexing command with repository path
- Configuration file loading and CLI argument parsing
- Progress reporting and verbose output options
- Error handling and user-friendly error messages

**Task 16: Main Indexer Orchestrator**
- Create `CodebaseIndexer` class integrating all components
- Implement `index_repository()` method for end-to-end processing
- File processing pipeline with error recovery
- Progress tracking and performance metrics
- Memory management for large repositories

**Task 17: Integration Testing and Validation**
- Create test repository samples for each supported language
- End-to-end integration tests for the complete pipeline
- Performance benchmarking with realistic codebases
- Error scenario testing (corrupted files, permission issues)
- Output validation and chunk quality assessment

#### 1.7 Testing and Documentation (Tasks 18-19)

**Task 18: Comprehensive Test Suite**
- Unit tests for all core modules with >90% coverage
- Integration tests for parser combinations
- Performance tests with large file scenarios
- Mock tests for external dependencies
- Test data generation and management

**Task 19: Documentation and Examples**
- Complete API documentation with docstrings
- Usage examples and tutorials
- Configuration reference documentation
- Troubleshooting guide and FAQ
- Performance tuning recommendations

#### Phase 1 Success Criteria

**Functional Requirements:**
- Successfully traverse and parse repositories with 10,000+ files
- Support for Python, JavaScript/TypeScript, and Markdown files
- Semantic chunking with proper metadata extraction
- Configurable ignore patterns and file filtering
- Robust error handling and recovery

**Performance Requirements:**
- Process 1,000 files per minute on standard hardware
- Memory usage under 1GB for typical repositories
- Graceful handling of files up to 10MB
- Progress reporting for operations >30 seconds

**Quality Requirements:**
- >90% test coverage for all core modules
- Zero critical bugs in file traversal and parsing
- Comprehensive logging and error reporting
- Clean, maintainable code following Python best practices

### Phase 2: Add embedding generation and storage
### Phase 3: Optimize performance and add incremental updates
### Phase 4: Comprehensive testing and documentation
### Phase 5: Integration with MCP server component
