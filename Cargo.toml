[package]
name = "popcorn-cli"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
clap = { version = "4.5.3", features = ["derive"] }
reqwest = { version = "0.11", features = ["json", "multipart"] }
tokio = { version = "1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
ratatui = "0.26.1"
crossterm = "0.27.0"
anyhow = "1.0"
ctrlc = "3.4.6"
dirs = "5.0"
serde_yaml = "0.9"
webbrowser = "0.8"
base64-url = "3.0.0"
urlencoding = "2.1.3"
bytes = "1.10.1"
futures-util = "0.3.31"


